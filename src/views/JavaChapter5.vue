<template>
  <div class="java-chapter5">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第五章：Java Concurrency Fundamentals</h1>
          <p class="chapter-subtitle">深入并发编程：从理论基础到字节码实现</p>
          <div class="chapter-badge">
            <span class="badge-text">Concurrency</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 并发理论入门 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="并发理论入门 (Concurrency Theory Primer)"
                :concept-data="concurrencyTheoryData"
                @interaction="handleInteraction"
              >
                <!-- SVG动画演示组件 -->
                <ConcurrencyAnimation />

                <div class="concurrency-theory-showcase">
                  <h3>🚀 并发编程：多核时代的必然选择</h3>

                  <div class="hardware-performance">
                    <h4>💻 硬件与性能的基本关系</h4>
                    <div class="performance-evolution">
                      <div class="evolution-card past">
                        <div class="evolution-header">
                          <span class="evolution-icon">🐌</span>
                          <h5>单核时代 (1970-2005)</h5>
                        </div>
                        <div class="evolution-content">
                          <p>性能提升依赖CPU频率增长</p>
                          <div class="evolution-stats">
                            <span>频率：1-3 GHz</span>
                            <span>核心：1个</span>
                            <span>策略：提高时钟频率</span>
                            <span>瓶颈：功耗墙、内存墙</span>
                          </div>
                          <div class="evolution-details">
                            <h6>技术特点</h6>
                            <ul>
                              <li>摩尔定律驱动：每18个月性能翻倍</li>
                              <li>指令级并行：超标量、乱序执行</li>
                              <li>缓存优化：多级缓存层次结构</li>
                              <li>分支预测：减少流水线停顿</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="evolution-arrow">→</div>

                      <div class="evolution-card present">
                        <div class="evolution-header">
                          <span class="evolution-icon">🚀</span>
                          <h5>多核时代 (2005-至今)</h5>
                        </div>
                        <div class="evolution-content">
                          <p>性能提升依赖并行计算</p>
                          <div class="evolution-stats">
                            <span>频率：2-4 GHz</span>
                            <span>核心：4-128个</span>
                            <span>策略：并发执行</span>
                            <span>挑战：并发复杂性</span>
                          </div>
                          <div class="evolution-details">
                            <h6>技术特点</h6>
                            <ul>
                              <li>线程级并行：多核心同时执行</li>
                              <li>NUMA架构：非统一内存访问</li>
                              <li>缓存一致性：MESI协议等</li>
                              <li>异构计算：CPU+GPU协同</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="amdahl-law">
                    <h4>📊 阿姆达尔定律 (Amdahl's Law)</h4>
                    <div class="law-explanation">
                      <div class="law-formula">
                        <h5>加速比公式</h5>
                        <div class="formula-display">
                          <span class="formula">S = 1 / (1 - P + P/N)</span>
                        </div>
                        <div class="formula-legend">
                          <div class="legend-item">
                            <span class="variable">S</span>
                            <span class="description">加速比 (Speedup)</span>
                          </div>
                          <div class="legend-item">
                            <span class="variable">P</span>
                            <span class="description">可并行部分比例 (0-1)</span>
                          </div>
                          <div class="legend-item">
                            <span class="variable">N</span>
                            <span class="description">处理器数量</span>
                          </div>
                        </div>
                      </div>

                      <div class="law-examples">
                        <h5>📈 实际计算示例</h5>
                        <div class="examples-grid">
                          <div class="example-card">
                            <h6>场景1：90%可并行</h6>
                            <div class="calculation">
                              <p>P = 0.9, N = 4核</p>
                              <p>S = 1/(0.1 + 0.9/4) = 1/0.325 ≈ 3.08倍</p>
                              <span class="result good">效果：较好</span>
                            </div>
                          </div>
                          <div class="example-card">
                            <h6>场景2：50%可并行</h6>
                            <div class="calculation">
                              <p>P = 0.5, N = 4核</p>
                              <p>S = 1/(0.5 + 0.5/4) = 1/0.625 = 1.6倍</p>
                              <span class="result poor">效果：一般</span>
                            </div>
                          </div>
                          <div class="example-card">
                            <h6>场景3：99%可并行</h6>
                            <div class="calculation">
                              <p>P = 0.99, N = 100核</p>
                              <p>S = 1/(0.01 + 0.99/100) ≈ 50倍</p>
                              <span class="result excellent">效果：优秀</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="law-insights">
                        <h5>💡 关键洞察与现实挑战</h5>
                        <div class="insight-grid">
                          <div class="insight-card critical">
                            <span class="insight-icon">⚠️</span>
                            <div>
                              <h6>串行瓶颈定律</h6>
                              <p>即使1%的串行代码也会将最大加速比限制在100倍</p>
                              <small>这就是为什么无限增加CPU核心数无法带来线性性能提升</small>
                            </div>
                          </div>
                          <div class="insight-card optimization">
                            <span class="insight-icon">🎯</span>
                            <div>
                              <h6>优化策略</h6>
                              <p>减少串行部分比增加处理器更有效</p>
                              <small>将串行比例从10%降到5%比CPU核心数翻倍更有价值</small>
                            </div>
                          </div>
                          <div class="insight-card reality">
                            <span class="insight-icon">🔍</span>
                            <div>
                              <h6>现实复杂性</h6>
                              <p>实际系统还面临缓存竞争、同步开销、负载不均等问题</p>
                              <small>理论加速比往往高于实际加速比</small>
                            </div>
                          </div>
                          <div class="insight-card gustafson">
                            <span class="insight-icon">📏</span>
                            <div>
                              <h6>古斯塔夫森定律</h6>
                              <p>固定时间内，问题规模可以随处理器数量扩展</p>
                              <small>S = N - (1-P)(N-1)，更乐观的并行性能模型</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="java-thread-model">
                    <h4>☕ Java线程模型深度解析</h4>
                    <div class="thread-model-overview">
                      <div class="model-characteristics">
                        <div class="characteristic-item">
                          <span class="char-icon">🔄</span>
                          <div>
                            <h5>共享可变状态 (Shared Mutable State)</h5>
                            <p>多个线程可以访问和修改同一块内存区域</p>
                            <div class="characteristic-details">
                              <ul>
                                <li>堆内存：所有线程共享对象实例</li>
                                <li>方法区：类信息、常量池、静态变量</li>
                                <li>直接内存：NIO、Unsafe操作的内存区域</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                        <div class="characteristic-item">
                          <span class="char-icon">⚡</span>
                          <div>
                            <h5>抢占式调度 (Preemptive Scheduling)</h5>
                            <p>操作系统可以在任意时刻中断线程执行</p>
                            <div class="characteristic-details">
                              <ul>
                                <li>时间片轮转：每个线程获得固定时间片</li>
                                <li>优先级调度：高优先级线程优先执行</li>
                                <li>中断机制：系统调用、I/O等会触发上下文切换</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                        <div class="characteristic-item">
                          <span class="char-icon">🧵</span>
                          <div>
                            <h5>线程私有区域</h5>
                            <p>每个线程拥有独立的执行环境</p>
                            <div class="characteristic-details">
                              <ul>
                                <li>程序计数器：记录当前执行指令位置</li>
                                <li>虚拟机栈：存储局部变量、方法参数</li>
                                <li>本地方法栈：支持native方法调用</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="memory-model-diagram">
                        <h5>🧠 Java内存模型概览</h5>
                        <div class="memory-layout">
                          <div class="memory-section shared">
                            <h6>共享内存区域</h6>
                            <div class="memory-item heap">堆内存 (Heap)</div>
                            <div class="memory-item method">方法区 (Method Area)</div>
                            <div class="memory-item direct">直接内存 (Direct Memory)</div>
                          </div>
                          <div class="memory-section private">
                            <h6>线程私有区域</h6>
                            <div class="thread-memory">
                              <div class="memory-item pc">程序计数器</div>
                              <div class="memory-item stack">虚拟机栈</div>
                              <div class="memory-item native">本地方法栈</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="core-contradiction">
                        <h5>🎭 核心矛盾：数据不一致问题</h5>
                        <div class="contradiction-demo">
                          <div class="demo-scenario">
                            <h6>经典场景：银行转账</h6>
                            <pre class="code-block">
// 线程A：转出操作
account1.balance -= 100;  // 步骤1：读取-修改-写入
account2.balance += 100;  // 步骤2：读取-修改-写入

// 线程B：查询总额
total = account1.balance + account2.balance;  // 可能在步骤1和2之间执行</pre
                            >
                          </div>
                          <div class="demo-problem">
                            <span class="problem-icon">❌</span>
                            <p>结果：总额可能暂时减少100，违反了不变性约束</p>
                          </div>
                          <div class="problem-analysis">
                            <h6>问题根源分析</h6>
                            <div class="analysis-points">
                              <div class="analysis-point">
                                <span class="point-icon">1️⃣</span>
                                <p><strong>原子性缺失：</strong>转账操作包含多个步骤，不是原子的</p>
                              </div>
                              <div class="analysis-point">
                                <span class="point-icon">2️⃣</span>
                                <p><strong>可见性问题：</strong>线程A的修改可能对线程B不立即可见</p>
                              </div>
                              <div class="analysis-point">
                                <span class="point-icon">3️⃣</span>
                                <p><strong>有序性问题：</strong>编译器和CPU可能重排序指令执行</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 并发设计核心概念 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="并发设计核心概念 (Design Concepts)"
                :concept-data="designConceptsData"
                @interaction="handleInteraction"
              >
                <div class="design-concepts-showcase">
                  <h3>🎯 并发程序设计的四大支柱</h3>

                  <div class="design-pillars">
                    <div class="pillar-grid">
                      <div class="pillar-card safety">
                        <div class="pillar-header">
                          <span class="pillar-icon">🛡️</span>
                          <h4>安全性 (Safety)</h4>
                          <div class="pillar-motto">"永远不会发生坏事"</div>
                        </div>
                        <div class="pillar-content">
                          <div class="pillar-definition">
                            <h5>核心定义</h5>
                            <p>确保程序在任何执行顺序下都不会进入不一致状态</p>
                          </div>
                          <div class="pillar-examples">
                            <h5>关键技术</h5>
                            <ul>
                              <li>原子性操作</li>
                              <li>同步机制</li>
                              <li>不可变对象</li>
                              <li>线程安全的数据结构</li>
                            </ul>
                          </div>
                          <div class="pillar-violations">
                            <h5>常见违反</h5>
                            <div class="violation-example">
                              <code>竞态条件、数据竞争、ABA问题</code>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="pillar-card liveness">
                        <div class="pillar-header">
                          <span class="pillar-icon">💓</span>
                          <h4>活性 (Liveness)</h4>
                          <div class="pillar-motto">"好事终将发生"</div>
                        </div>
                        <div class="pillar-content">
                          <div class="pillar-definition">
                            <h5>核心定义</h5>
                            <p>确保程序能够持续进展，不会永久阻塞</p>
                          </div>
                          <div class="pillar-examples">
                            <h5>关键技术</h5>
                            <ul>
                              <li>死锁预防</li>
                              <li>超时机制</li>
                              <li>优先级调度</li>
                              <li>无锁算法</li>
                            </ul>
                          </div>
                          <div class="pillar-violations">
                            <h5>常见违反</h5>
                            <div class="violation-example">
                              <code>死锁、活锁、饥饿</code>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="pillar-card performance">
                        <div class="pillar-header">
                          <span class="pillar-icon">⚡</span>
                          <h4>性能 (Performance)</h4>
                          <div class="pillar-motto">"更快更高效"</div>
                        </div>
                        <div class="pillar-content">
                          <div class="pillar-definition">
                            <h5>核心定义</h5>
                            <p>在保证正确性的前提下，最大化系统吞吐量和响应速度</p>
                          </div>
                          <div class="pillar-examples">
                            <h5>关键技术</h5>
                            <ul>
                              <li>减少锁竞争</li>
                              <li>缓存友好设计</li>
                              <li>批处理优化</li>
                              <li>异步处理</li>
                            </ul>
                          </div>
                          <div class="pillar-violations">
                            <h5>常见问题</h5>
                            <div class="violation-example">
                              <code>过度同步、上下文切换、缓存失效</code>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="pillar-card reusability">
                        <div class="pillar-header">
                          <span class="pillar-icon">🔄</span>
                          <h4>可重用性 (Reusability)</h4>
                          <div class="pillar-motto">"设计一次，处处使用"</div>
                        </div>
                        <div class="pillar-content">
                          <div class="pillar-definition">
                            <h5>核心定义</h5>
                            <p>创建可在不同场景下安全使用的并发组件</p>
                          </div>
                          <div class="pillar-examples">
                            <h5>关键技术</h5>
                            <ul>
                              <li>线程安全保证</li>
                              <li>清晰的契约定义</li>
                              <li>组合性设计</li>
                              <li>文档化的并发行为</li>
                            </ul>
                          </div>
                          <div class="pillar-violations">
                            <h5>常见问题</h5>
                            <div class="violation-example">
                              <code>隐式依赖、状态泄露、不明确的线程安全性</code>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="design-tradeoffs">
                    <h4>⚖️ 设计权衡</h4>
                    <div class="tradeoff-matrix">
                      <div class="tradeoff-item">
                        <div class="tradeoff-pair">
                          <span class="tradeoff-left">安全性</span>
                          <span class="tradeoff-vs">vs</span>
                          <span class="tradeoff-right">性能</span>
                        </div>
                        <p>更多的同步保证安全，但可能降低并发度</p>
                      </div>
                      <div class="tradeoff-item">
                        <div class="tradeoff-pair">
                          <span class="tradeoff-left">活性</span>
                          <span class="tradeoff-vs">vs</span>
                          <span class="tradeoff-right">安全性</span>
                        </div>
                        <p>避免死锁可能需要放宽某些安全约束</p>
                      </div>
                      <div class="tradeoff-item">
                        <div class="tradeoff-pair">
                          <span class="tradeoff-left">可重用性</span>
                          <span class="tradeoff-vs">vs</span>
                          <span class="tradeoff-right">性能</span>
                        </div>
                        <p>通用设计往往比特定优化性能稍差</p>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 块结构并发 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="块结构并发 (Block-structured Concurrency)"
                :concept-data="blockConcurrencyData"
                @interaction="handleInteraction"
              >
                <div class="block-concurrency-showcase">
                  <h3>🔒 synchronized：Java并发的基石</h3>

                  <div class="synchronized-overview">
                    <h4>🛡️ synchronized关键字</h4>
                    <div class="sync-explanation">
                      <div class="sync-concept">
                        <h5>核心机制</h5>
                        <p>
                          synchronized基于对象监视器(monitor)实现互斥访问，确保同一时刻只有一个线程能执行被保护的代码块。
                        </p>
                      </div>

                      <div class="sync-forms">
                        <h5>使用形式</h5>
                        <div class="forms-grid">
                          <div class="form-card">
                            <h6>同步方法</h6>
                            <pre class="code-block">
public synchronized void method() {
    // 锁定this对象
}</pre
                            >
                          </div>
                          <div class="form-card">
                            <h6>同步代码块</h6>
                            <pre class="code-block">
synchronized(lockObject) {
    // 锁定指定对象
}</pre
                            >
                          </div>
                          <div class="form-card">
                            <h6>静态同步方法</h6>
                            <pre class="code-block">
public static synchronized void method() {
    // 锁定Class对象
}</pre
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="thread-states">
                    <h4>🔄 线程状态模型</h4>
                    <div class="states-diagram">
                      <div class="state-flow">
                        <div class="state-node new">
                          <span class="state-name">NEW</span>
                          <span class="state-desc">线程创建但未启动</span>
                        </div>
                        <div class="flow-arrow">start()</div>
                        <div class="state-node runnable">
                          <span class="state-name">RUNNABLE</span>
                          <span class="state-desc">可运行状态</span>
                        </div>
                        <div class="flow-arrow">等待锁</div>
                        <div class="state-node blocked">
                          <span class="state-name">BLOCKED</span>
                          <span class="state-desc">等待监视器锁</span>
                        </div>
                      </div>

                      <div class="state-flow">
                        <div class="state-node waiting">
                          <span class="state-name">WAITING</span>
                          <span class="state-desc">无限期等待</span>
                        </div>
                        <div class="flow-arrow">wait()/join()</div>
                        <div class="state-node timed-waiting">
                          <span class="state-name">TIMED_WAITING</span>
                          <span class="state-desc">限时等待</span>
                        </div>
                        <div class="flow-arrow">超时/notify()</div>
                        <div class="state-node terminated">
                          <span class="state-name">TERMINATED</span>
                          <span class="state-desc">线程结束</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="deadlock-analysis">
                    <h4>💀 死锁问题及解决方案</h4>
                    <div class="deadlock-demo">
                      <div class="deadlock-scenario">
                        <h5>经典死锁场景</h5>
                        <div class="deadlock-code">
                          <div class="thread-code">
                            <h6>线程A</h6>
                            <pre class="code-block">
synchronized(lock1) {
    // 获得lock1
    Thread.sleep(100);
    synchronized(lock2) {
        // 等待lock2
    }
}</pre
                            >
                          </div>
                          <div class="thread-code">
                            <h6>线程B</h6>
                            <pre class="code-block">
synchronized(lock2) {
    // 获得lock2
    Thread.sleep(100);
    synchronized(lock1) {
        // 等待lock1
    }
}</pre
                            >
                          </div>
                        </div>
                      </div>

                      <div class="deadlock-solutions">
                        <h5>解决策略</h5>
                        <div class="solution-grid">
                          <div class="solution-card">
                            <span class="solution-icon">🔢</span>
                            <div>
                              <h6>锁排序</h6>
                              <p>所有线程按相同顺序获取锁</p>
                            </div>
                          </div>
                          <div class="solution-card">
                            <span class="solution-icon">⏰</span>
                            <div>
                              <h6>超时机制</h6>
                              <p>使用tryLock()设置获取锁的超时时间</p>
                            </div>
                          </div>
                          <div class="solution-card">
                            <span class="solution-icon">🔍</span>
                            <div>
                              <h6>死锁检测</h6>
                              <p>运行时检测并打破死锁循环</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="volatile-keyword">
                    <h4>⚡ volatile关键字：可见性保证</h4>
                    <div class="volatile-explanation">
                      <div class="volatile-purpose">
                        <h5>设计目的</h5>
                        <p>
                          volatile确保变量的修改对所有线程立即可见，防止编译器和CPU的优化导致的可见性问题。
                        </p>
                      </div>

                      <div class="volatile-guarantees">
                        <h5>提供的保证</h5>
                        <div class="guarantee-list">
                          <div class="guarantee-item">
                            <span class="guarantee-icon">👁️</span>
                            <div>
                              <h6>可见性</h6>
                              <p>对volatile变量的写操作立即刷新到主内存</p>
                            </div>
                          </div>
                          <div class="guarantee-item">
                            <span class="guarantee-icon">🚫</span>
                            <div>
                              <h6>禁止重排序</h6>
                              <p>volatile读写前后的操作不会被重排序</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="volatile-limitations">
                        <h5>⚠️ 局限性</h5>
                        <div class="limitation-example">
                          <pre class="code-block">
volatile int count = 0;

// 这不是原子操作！
count++;  // 实际上是：读取 → 加1 → 写回

// 多线程环境下仍可能丢失更新</pre
                          >
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="immutability">
                    <h4>🔒 不可变性：最佳实践</h4>
                    <div class="immutability-benefits">
                      <div class="benefit-overview">
                        <h5>为什么不可变对象是并发编程的圣杯？</h5>
                        <div class="benefits-grid">
                          <div class="benefit-item">
                            <span class="benefit-icon">🛡️</span>
                            <div>
                              <h6>天然线程安全</h6>
                              <p>无法修改，就不存在竞态条件</p>
                            </div>
                          </div>
                          <div class="benefit-item">
                            <span class="benefit-icon">🚀</span>
                            <div>
                              <h6>无需同步</h6>
                              <p>可以自由共享，无性能开销</p>
                            </div>
                          </div>
                          <div class="benefit-item">
                            <span class="benefit-icon">🧠</span>
                            <div>
                              <h6>简化推理</h6>
                              <p>状态不变，程序行为可预测</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="immutable-design">
                        <h5>设计模式</h5>
                        <pre class="code-block">
public final class ImmutablePerson {
    private final String name;
    private final int age;
    private final List&lt;String&gt; hobbies;

    public ImmutablePerson(String name, int age, List&lt;String&gt; hobbies) {
        this.name = name;
        this.age = age;
        // 防御性复制
        this.hobbies = Collections.unmodifiableList(new ArrayList&lt;&gt;(hobbies));
    }

    // 只提供getter，不提供setter
    public String getName() { return name; }
    public int getAge() { return age; }
    public List&lt;String&gt; getHobbies() { return hobbies; }
}</pre
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: Java内存模型 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="Java内存模型 (JMM - Java Memory Model)"
                :concept-data="jmmData"
                @interaction="handleInteraction"
              >
                <div class="jmm-showcase">
                  <h3>🧠 Java内存模型：并发程序的行为规范</h3>

                  <div class="jmm-definition">
                    <h4>📋 JMM的定义和存在价值</h4>
                    <div class="definition-overview">
                      <div class="definition-card">
                        <div class="definition-header">
                          <span class="definition-icon">🎯</span>
                          <h5>什么是JMM？</h5>
                        </div>
                        <p>
                          Java内存模型定义了多线程程序中变量的访问规则，规定了一个线程对共享变量的写入何时对另一个线程可见。
                        </p>
                      </div>

                      <div class="definition-card">
                        <div class="definition-header">
                          <span class="definition-icon">🤔</span>
                          <h5>为什么需要JMM？</h5>
                        </div>
                        <div class="need-reasons">
                          <div class="reason-item">
                            <span class="reason-icon">🔧</span>
                            <div>
                              <h6>硬件差异</h6>
                              <p>不同CPU架构有不同的内存一致性模型</p>
                            </div>
                          </div>
                          <div class="reason-item">
                            <span class="reason-icon">⚡</span>
                            <div>
                              <h6>编译器优化</h6>
                              <p>编译器可能重排序指令以提高性能</p>
                            </div>
                          </div>
                          <div class="reason-item">
                            <span class="reason-icon">🎭</span>
                            <div>
                              <h6>抽象统一</h6>
                              <p>为Java程序员提供一致的并发语义</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="happens-before">
                    <h4>🔗 Happens-Before关系</h4>
                    <div class="hb-explanation">
                      <div class="hb-concept">
                        <h5>核心概念</h5>
                        <p>如果操作A happens-before操作B，那么A的结果对B可见，且A在B之前执行。</p>
                      </div>

                      <div class="hb-rules">
                        <h5>核心规则</h5>
                        <div class="rules-grid">
                          <div class="rule-card program-order">
                            <div class="rule-header">
                              <span class="rule-icon">📝</span>
                              <h6>程序顺序规则</h6>
                            </div>
                            <p>单线程内，按程序代码顺序，前面的操作happens-before后面的操作</p>
                            <div class="rule-example">
                              <code>int a = 1; int b = 2; // a的赋值 happens-before b的赋值</code>
                            </div>
                          </div>

                          <div class="rule-card monitor-lock">
                            <div class="rule-header">
                              <span class="rule-icon">🔒</span>
                              <h6>锁规则</h6>
                            </div>
                            <p>对同一个锁的unlock操作happens-before后续的lock操作</p>
                            <div class="rule-example">
                              <code
                                >synchronized块的退出 happens-before
                                下一个synchronized块的进入</code
                              >
                            </div>
                          </div>

                          <div class="rule-card volatile-rule">
                            <div class="rule-header">
                              <span class="rule-icon">⚡</span>
                              <h6>volatile规则</h6>
                            </div>
                            <p>对volatile变量的写操作happens-before后续的读操作</p>
                            <div class="rule-example">
                              <code>volatile写 happens-before volatile读</code>
                            </div>
                          </div>

                          <div class="rule-card transitivity">
                            <div class="rule-header">
                              <span class="rule-icon">🔄</span>
                              <h6>传递性规则</h6>
                            </div>
                            <p>如果A happens-before B，B happens-before C，则A happens-before C</p>
                            <div class="rule-example">
                              <code>A → B → C 则 A → C</code>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="minimal-guarantees">
                    <h4>🛡️ 最小保证原则</h4>
                    <div class="guarantees-explanation">
                      <div class="guarantee-principle">
                        <h5>设计哲学</h5>
                        <p>JMM只提供最小必要的同步保证，在正确性和性能之间找到平衡。</p>
                      </div>

                      <div class="guarantee-examples">
                        <h5>具体保证</h5>
                        <div class="guarantee-grid">
                          <div class="guarantee-item safe">
                            <span class="guarantee-icon">✅</span>
                            <div>
                              <h6>安全发布</h6>
                              <p>正确同步的对象引用发布是安全的</p>
                            </div>
                          </div>
                          <div class="guarantee-item safe">
                            <span class="guarantee-icon">✅</span>
                            <div>
                              <h6>final字段</h6>
                              <p>final字段在构造函数完成后对所有线程可见</p>
                            </div>
                          </div>
                          <div class="guarantee-item unsafe">
                            <span class="guarantee-icon">❌</span>
                            <div>
                              <h6>普通字段</h6>
                              <p>没有同步的普通字段访问不保证可见性</p>
                            </div>
                          </div>
                          <div class="guarantee-item unsafe">
                            <span class="guarantee-icon">❌</span>
                            <div>
                              <h6>重排序</h6>
                              <p>编译器和CPU可能重排序没有依赖关系的操作</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: 通过字节码理解并发 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="通过字节码理解并发 (Understanding Concurrency through Bytecode)"
                :concept-data="bytecodeData"
                @interaction="handleInteraction"
              >
                <div class="bytecode-concurrency-showcase">
                  <h3>🔍 字节码视角：并发问题的根本原因</h3>

                  <div class="lost-update-analysis">
                    <h4>💥 丢失更新问题的字节码分析</h4>
                    <div class="lost-update-demo">
                      <div class="source-code">
                        <h5>Java源代码</h5>
                        <pre class="code-block">
public class Counter {
    private int count = 0;

    public void increment() {
        count++;  // 看似原子的操作
    }
}</pre
                        >
                      </div>

                      <div class="bytecode-breakdown">
                        <h5>字节码分解</h5>
                        <pre class="code-block">
public void increment();
  Code:
     0: aload_0          // 加载this引用
     1: dup              // 复制this引用
     2: getfield #2      // 获取count字段值
     5: iconst_1         // 加载常量1
     6: iadd             // 执行加法运算
     7: putfield #2      // 将结果写回count字段
    10: return</pre
                        >
                      </div>

                      <div class="concurrency-problem">
                        <h5>并发问题分析</h5>
                        <div class="problem-scenario">
                          <div class="thread-execution">
                            <h6>线程A执行</h6>
                            <div class="execution-steps">
                              <div class="step">getfield: 读取count=5</div>
                              <div class="step">iadd: 计算5+1=6</div>
                              <div class="step interrupted">被中断...</div>
                            </div>
                          </div>
                          <div class="thread-execution">
                            <h6>线程B执行</h6>
                            <div class="execution-steps">
                              <div class="step">getfield: 读取count=5</div>
                              <div class="step">iadd: 计算5+1=6</div>
                              <div class="step">putfield: 写入count=6</div>
                            </div>
                          </div>
                          <div class="thread-execution">
                            <h6>线程A继续</h6>
                            <div class="execution-steps">
                              <div class="step">putfield: 写入count=6</div>
                              <div class="step problem">结果：丢失了一次更新！</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="synchronized-bytecode">
                    <h4>🔒 synchronized的字节码实现</h4>
                    <div class="sync-bytecode-analysis">
                      <div class="sync-source">
                        <h5>同步方法</h5>
                        <pre class="code-block">
public synchronized void increment() {
    count++;
}</pre
                        >
                      </div>

                      <div class="sync-bytecode">
                        <h5>字节码实现</h5>
                        <pre class="code-block">
public synchronized void increment();
  descriptor: ()V
  flags: ACC_PUBLIC, ACC_SYNCHRONIZED  // 方法标志包含同步
  Code:
     0: aload_0
     1: dup
     2: getfield #2
     5: iconst_1
     6: iadd
     7: putfield #2
    10: return</pre
                        >
                      </div>

                      <div class="monitor-instructions">
                        <h5>同步代码块的字节码</h5>
                        <pre class="code-block">
synchronized(this) {
    count++;
}

// 对应字节码：
 0: aload_0
 1: dup
 2: astore_1
 3: monitorenter      // 获取监视器锁
 4: aload_0
 5: dup
 6: getfield #2
 9: iconst_1
10: iadd
11: putfield #2
14: aload_1
15: monitorexit       // 释放监视器锁
16: goto 24
19: astore_2
20: aload_1
21: monitorexit       // 异常处理中也要释放锁
22: aload_2
23: athrow
24: return</pre
                        >
                      </div>
                    </div>
                  </div>

                  <div class="unsafe-reads">
                    <h4>⚠️ 非同步读的危险性</h4>
                    <div class="unsafe-read-demo">
                      <div class="read-scenario">
                        <h5>场景：64位值的读取</h5>
                        <pre class="code-block">
public class UnsafeRead {
    private long value = 0;

    public void setValue(long newValue) {
        this.value = newValue;  // 可能不是原子操作
    }

    public long getValue() {
        return this.value;      // 可能读到"撕裂"的值
    }
}</pre
                        >
                      </div>

                      <div class="tear-explanation">
                        <h5>💔 值撕裂现象</h5>
                        <div class="tear-demo">
                          <div class="tear-scenario">
                            <h6>32位JVM上的问题</h6>
                            <p>long和double值需要两次32位操作</p>
                            <div class="tear-steps">
                              <div class="step">写入高32位：0x12345678</div>
                              <div class="step interrupted">线程切换...</div>
                              <div class="step">读取操作可能得到：0x12345678XXXXXXXX</div>
                              <div class="step">写入低32位：0x9ABCDEF0</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="volatile-bytecode">
                    <h4>⚡ volatile的字节码访问机制</h4>
                    <div class="volatile-implementation">
                      <div class="volatile-source">
                        <h5>volatile字段访问</h5>
                        <pre class="code-block">
public class VolatileExample {
    private volatile boolean flag = false;

    public void setFlag() {
        flag = true;  // volatile写
    }

    public boolean getFlag() {
        return flag;  // volatile读
    }
}</pre
                        >
                      </div>

                      <div class="volatile-bytecode-analysis">
                        <h5>字节码层面的保证</h5>
                        <div class="volatile-guarantees">
                          <div class="guarantee-item">
                            <span class="guarantee-icon">🚫</span>
                            <div>
                              <h6>内存屏障</h6>
                              <p>JVM在volatile访问前后插入内存屏障指令</p>
                            </div>
                          </div>
                          <div class="guarantee-item">
                            <span class="guarantee-icon">🔄</span>
                            <div>
                              <h6>缓存一致性</h6>
                              <p>强制从主内存读取，立即刷新到主内存</p>
                            </div>
                          </div>
                          <div class="guarantee-item">
                            <span class="guarantee-icon">📐</span>
                            <div>
                              <h6>禁止重排序</h6>
                              <p>volatile访问不会与其他内存操作重排序</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- 章节总结与思维导图 -->
            <section id="topic-5" class="topic-section chapter-summary" ref="topic5">
              <ExpandableSection
                title="📊 章节总结与知识体系图"
                :concept-data="chapterSummaryData"
                @interaction="handleInteraction"
              >
                <div class="summary-content">
                  <h3>🎯 本章核心收获</h3>
                  <div class="key-takeaways">
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🚀</span>
                      <div>
                        <h4>并发理论基础</h4>
                        <p>理解多核驱动并发需求，掌握阿姆达尔定律的性能限制</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🎯</span>
                      <div>
                        <h4>设计核心概念</h4>
                        <p>掌握安全性、活性、性能、可重用性四大设计支柱</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔒</span>
                      <div>
                        <h4>块结构并发</h4>
                        <p>深入理解synchronized、volatile和不可变性的应用</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🧠</span>
                      <div>
                        <h4>Java内存模型</h4>
                        <p>掌握happens-before关系和JMM的最小保证原则</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔍</span>
                      <div>
                        <h4>字节码并发分析</h4>
                        <p>从字节码层面理解并发问题的根本原因和解决机制</p>
                      </div>
                    </div>
                  </div>

                  <div class="mind-map-section">
                    <h3>🗺️ 知识体系思维导图</h3>
                    <div class="mindmap-container">
                      <div class="mindmap-wrapper">
                        <div id="concurrency-mindmap" class="mermaid-container"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import ConcurrencyAnimation from '@/components/ConcurrencyAnimation.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: '并发理论入门',
    description: '多核驱动并发需求与阿姆达尔定律',
  },
  {
    title: '并发设计核心概念',
    description: '安全性、活性、性能、可重用性四大支柱',
  },
  {
    title: '块结构并发',
    description: 'synchronized、volatile与不可变性',
  },
  {
    title: 'Java内存模型',
    description: 'JMM与happens-before关系',
  },
  {
    title: '通过字节码理解并发',
    description: '字节码层面的并发问题分析',
  },
]

// 概念数据
const concurrencyTheoryData = {
  keyPoints: [
    '多核时代性能提升依赖并行计算而非频率增长',
    '阿姆达尔定律揭示串行部分对并行效果的限制',
    'Java线程模型基于共享可变状态和抢占式调度',
    '数据不一致是并发编程的核心矛盾',
    '理解硬件架构对并发程序设计的影响',
  ],
}

const designConceptsData = {
  keyPoints: [
    '安全性确保程序永远不会进入不一致状态',
    '活性保证程序能够持续进展不会永久阻塞',
    '性能在正确性前提下最大化吞吐量和响应速度',
    '可重用性创建可在不同场景下安全使用的组件',
    '四大支柱之间存在设计权衡需要平衡考虑',
  ],
}

const blockConcurrencyData = {
  keyPoints: [
    'synchronized基于对象监视器实现互斥访问',
    '线程状态模型包含NEW、RUNNABLE、BLOCKED等状态',
    '死锁可通过锁排序、超时机制、检测等方式解决',
    'volatile提供可见性保证但不保证原子性',
    '不可变对象是并发编程的最佳实践',
  ],
}

const jmmData = {
  keyPoints: [
    'JMM定义多线程程序中变量访问规则',
    'happens-before关系确定操作间的可见性和顺序',
    '程序顺序、锁规则、volatile规则、传递性是核心规则',
    'JMM只提供最小必要的同步保证',
    '理解JMM有助于编写正确的并发程序',
  ],
}

const bytecodeData = {
  keyPoints: [
    '看似原子的操作在字节码层面可能包含多个步骤',
    'synchronized通过monitorenter/monitorexit实现',
    '64位值的读写在32位JVM上可能出现值撕裂',
    'volatile通过内存屏障保证可见性和禁止重排序',
    '字节码分析有助于理解并发问题的根本原因',
  ],
}

const chapterSummaryData = {
  keyPoints: [
    '掌握并发编程的理论基础和设计原则',
    '理解Java并发机制的底层实现',
    '学会分析和解决常见并发问题',
    '建立正确的并发程序设计思维',
  ],
}

// 方法
const scrollToTopic = (index: number) => {
  currentTopic.value = index

  // 滚动到对应的主题部分
  const targetElement = document.getElementById(`topic-${index}`)
  if (targetElement) {
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
}

// 生命周期
onMounted(async () => {
  // 渲染思维导图
  await renderMindMap()

  // 模拟进度更新
  const interval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += 2
    } else {
      clearInterval(interval)
    }
  }, 100)
})

const renderMindMap = async () => {
  try {
    console.log('开始初始化 Mermaid...')
    const mermaid = await import('mermaid')
    console.log('Mermaid 模块加载成功:', mermaid)

    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    })
    console.log('Mermaid 初始化完成')

    // 延迟渲染以确保DOM已加载
    setTimeout(async () => {
      try {
        console.log('开始渲染 Mermaid 图表...')

        // 创建思维导图内容
        const mindmapContent = `mindmap
  root((Java并发基础))
    并发理论
      多核驱动
        单核时代局限
        多核性能提升
        硬件架构演进
      阿姆达尔定律
        加速比公式
        串行瓶颈
        优化策略
        古斯塔夫森定律
      Java线程模型
        共享可变状态
        抢占式调度
        内存模型
        线程私有区域
    设计概念
      安全性
        数据一致性
        原子性操作
        竞态条件
      活性
        死锁预防
        活锁避免
        饥饿解决
      性能
        吞吐量优化
        响应时间
        资源利用率
      可重用性
        线程安全设计
        组合性
        文档化
    块结构并发
      synchronized
        监视器锁
        方法同步
        代码块同步
      线程状态
        NEW状态
        RUNNABLE状态
        BLOCKED状态
        WAITING状态
        TERMINATED状态
      死锁解决
        锁排序
        超时机制
        死锁检测
      volatile
        可见性保证
        禁止重排序
        使用场景
      不可变性
        天然线程安全
        设计模式
        性能优势
    内存模型
      JMM定义
        访问规则
        可见性规范
        有序性保证
      happens-before
        程序顺序规则
        锁规则
        volatile规则
        传递性规则
      核心规则
        同步操作
        内存屏障
        缓存一致性
      最小保证
        安全发布
        final字段
        重排序限制
    字节码分析
      丢失更新
        非原子操作
        指令分解
        竞态条件
      监视器锁
        monitorenter
        monitorexit
        异常处理
      值撕裂
        64位操作
        原子性保证
        平台差异
      内存屏障
        LoadLoad屏障
        StoreStore屏障
        LoadStore屏障
        StoreLoad屏障`

        const container = document.getElementById('concurrency-mindmap')
        if (container) {
          console.log('找到容器，开始渲染...')
          const { svg } = await mermaid.default.render('concurrency-mindmap-svg', mindmapContent)
          container.innerHTML = svg
          console.log('Mermaid 图表渲染完成')
        } else {
          console.error('未找到思维导图容器')
        }
      } catch (renderError) {
        console.error('Mermaid 渲染错误:', renderError)
        // 渲染失败时显示占位符
        const container = document.getElementById('concurrency-mindmap')
        if (container) {
          container.innerHTML = `
            <div class="mindmap-placeholder">
              <p>🗺️ 并发编程知识体系图</p>
              <p>包含理论基础、设计概念、实现机制、内存模型和字节码分析五大模块</p>
            </div>
          `
        }
      }
    }, 1000)
  } catch (error) {
    console.error('Mermaid 初始化失败:', error)
  }
}
</script>

<style scoped>
.java-chapter5 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cdefs%3E%3Cpattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"%3E%3Cpath d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100" height="100" fill="url(%23grid)"/%3E%3C/svg%3E');
  opacity: 0.3;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.chapter-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.chapter-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.chapter-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.badge-text {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.progress-bar {
  max-width: 400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 4px;
  position: relative;
}

.progress-fill {
  height: 8px;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: 600;
}

.content-wrapper {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 2rem 0;
  overflow-x: hidden; /* 防止水平溢出 */
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
  max-width: 100%; /* 确保不超出容器 */
  box-sizing: border-box;
}

.sidebar {
  position: sticky;
  top: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.outline {
  padding: 1.5rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.outline-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.outline-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.outline-item:hover {
  background: #f8f9fa;
  transform: translateX(4px);
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.outline-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.outline-content p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.7;
  line-height: 1.3;
}

.toolbar {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 0.5rem;
}

.tool-button {
  flex: 1;
  padding: 0.5rem;
  border: none;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8rem;
}

.tool-button:hover {
  background: #e9ecef;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 100%; /* 防止内容溢出 */
  overflow-x: hidden;
}

.topic-section {
  scroll-margin-top: 2rem;
  max-width: 100%;
  box-sizing: border-box;
}

/* 并发理论样式 */
.concurrency-theory-showcase {
  padding: 2rem;
  max-width: 100%;
  overflow-x: hidden;
}

/* 演进详情样式 */
.evolution-details {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.evolution-details h6 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.evolution-details ul {
  margin: 0;
  padding-left: 1rem;
  list-style-type: disc;
}

.evolution-details li {
  font-size: 0.8rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

/* 特征详情样式 */
.characteristic-details {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.characteristic-details ul {
  margin: 0;
  padding-left: 1rem;
  list-style-type: disc;
}

.characteristic-details li {
  font-size: 0.85rem;
  line-height: 1.3;
  margin-bottom: 0.25rem;
  color: #555;
}

.performance-evolution {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin: 2rem 0;
}

.evolution-card {
  flex: 1;
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
}

.evolution-card.past {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
}

.evolution-card.present {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
}

.evolution-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.evolution-icon {
  font-size: 2rem;
}

.evolution-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.evolution-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.law-explanation {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.law-formula {
  text-align: center;
  margin-bottom: 2rem;
}

.formula-display {
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.formula-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.variable {
  font-weight: bold;
  color: #667eea;
  font-size: 1.1rem;
}

.insight-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.insight-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.insight-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

/* 阿姆达尔定律增强样式 */
.law-examples {
  margin: 2rem 0;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.example-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #667eea;
}

.example-card h6 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.calculation {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
}

.calculation p {
  margin: 0.25rem 0;
  color: #555;
}

.result {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.result.good {
  background: #d4edda;
  color: #155724;
}

.result.poor {
  background: #f8d7da;
  color: #721c24;
}

.result.excellent {
  background: #d1ecf1;
  color: #0c5460;
}

/* 洞察卡片增强样式 */
.insight-card.critical {
  border-left: 4px solid #dc3545;
  background: #fff5f5;
}

.insight-card.optimization {
  border-left: 4px solid #28a745;
  background: #f8fff9;
}

.insight-card.reality {
  border-left: 4px solid #ffc107;
  background: #fffdf5;
}

.insight-card.gustafson {
  border-left: 4px solid #6f42c1;
  background: #f8f7ff;
}

.insight-card small {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #666;
  font-style: italic;
}

.thread-model-overview {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.model-characteristics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.characteristic-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.char-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

/* 内存模型图样式 */
.memory-model-diagram {
  margin: 2rem 0;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.memory-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.memory-section {
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
}

.memory-section.shared {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 2px solid #2196f3;
}

.memory-section.private {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  border: 2px solid #9c27b0;
}

.memory-section h6 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.memory-item {
  display: block;
  padding: 0.75rem;
  margin: 0.5rem 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.memory-item.heap {
  border-left: 4px solid #4caf50;
}

.memory-item.method {
  border-left: 4px solid #ff9800;
}

.memory-item.direct {
  border-left: 4px solid #f44336;
}

.memory-item.pc {
  border-left: 4px solid #2196f3;
}

.memory-item.stack {
  border-left: 4px solid #9c27b0;
}

.memory-item.native {
  border-left: 4px solid #607d8b;
}

.thread-memory {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 问题分析样式 */
.problem-analysis {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: #fff8e1;
  border-radius: 8px;
  border-left: 4px solid #ffc107;
}

.problem-analysis h6 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.analysis-points {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.analysis-point {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
}

.point-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.analysis-point p {
  margin: 0;
  line-height: 1.4;
}

.analysis-point strong {
  color: #d32f2f;
}

.contradiction-demo {
  background: #fff5f5;
  border: 2px solid #fed7d7;
  border-radius: 8px;
  padding: 1.5rem;
}

.demo-scenario {
  margin-bottom: 1rem;
}

.demo-problem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e53e3e;
  font-weight: 600;
}

.problem-icon {
  font-size: 1.2rem;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  overflow-x: auto;
  margin: 0.5rem 0;
}

/* 设计概念样式 */
.design-concepts-showcase {
  padding: 2rem;
}

.pillar-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.pillar-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.pillar-card:hover {
  transform: translateY(-4px);
}

.pillar-card.safety {
  border-top: 4px solid #48bb78;
}

.pillar-card.liveness {
  border-top: 4px solid #ed8936;
}

.pillar-card.performance {
  border-top: 4px solid #4299e1;
}

.pillar-card.reusability {
  border-top: 4px solid #9f7aea;
}

.pillar-header {
  padding: 1.5rem;
  text-align: center;
}

.pillar-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.pillar-motto {
  font-style: italic;
  color: #666;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.pillar-content {
  padding: 0 1.5rem 1.5rem;
}

.pillar-definition,
.pillar-examples,
.pillar-violations {
  margin-bottom: 1.5rem;
}

.pillar-examples ul {
  list-style: none;
  padding: 0;
}

.pillar-examples li {
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1rem;
}

.pillar-examples li::before {
  content: '▸';
  position: absolute;
  left: 0;
  color: #667eea;
}

.violation-example {
  background: #fff5f5;
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 4px solid #fed7d7;
  font-size: 0.9rem;
}

.tradeoff-matrix {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
}

.tradeoff-item {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tradeoff-pair {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.tradeoff-left,
.tradeoff-right {
  color: #667eea;
}

.tradeoff-vs {
  color: #e53e3e;
  font-weight: bold;
}

/* 块结构并发样式 */
.block-concurrency-showcase {
  padding: 2rem;
}

.sync-explanation {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.form-card {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.states-diagram {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.state-flow {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
  flex-wrap: wrap;
}

.state-node {
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  min-width: 120px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.state-node.new {
  background: #e6fffa;
  border: 2px solid #38b2ac;
}

.state-node.runnable {
  background: #f0fff4;
  border: 2px solid #48bb78;
}

.state-node.blocked {
  background: #fffaf0;
  border: 2px solid #ed8936;
}

.state-node.waiting {
  background: #faf5ff;
  border: 2px solid #9f7aea;
}

.state-node.timed-waiting {
  background: #ebf8ff;
  border: 2px solid #4299e1;
}

.state-node.terminated {
  background: #fed7d7;
  border: 2px solid #e53e3e;
}

.state-name {
  display: block;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.state-desc {
  font-size: 0.8rem;
  color: #666;
}

.flow-arrow {
  color: #667eea;
  font-weight: bold;
  font-size: 0.9rem;
}

.deadlock-demo {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.deadlock-code {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 1rem 0;
}

.thread-code {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #e53e3e;
}

.solution-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.solution-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f0fff4;
  border-radius: 8px;
  border-left: 4px solid #48bb78;
}

.solution-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.volatile-explanation {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.guarantee-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.guarantee-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.guarantee-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.limitation-example {
  background: #fff5f5;
  border: 2px solid #fed7d7;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.immutability-benefits {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f0fff4;
  border-radius: 8px;
  border-left: 4px solid #48bb78;
}

.benefit-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.immutable-design {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
}

/* JMM样式 */
.jmm-showcase {
  padding: 2rem;
}

.definition-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.definition-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.definition-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.definition-icon {
  font-size: 1.5rem;
}

.need-reasons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reason-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.reason-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.hb-explanation {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.rule-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #667eea;
}

.rule-card.program-order {
  border-left-color: #48bb78;
}

.rule-card.monitor-lock {
  border-left-color: #ed8936;
}

.rule-card.volatile-rule {
  border-left-color: #4299e1;
}

.rule-card.transitivity {
  border-left-color: #9f7aea;
}

.rule-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.rule-icon {
  font-size: 1.2rem;
}

.rule-example {
  background: #2d3748;
  color: #e2e8f0;
  padding: 0.75rem;
  border-radius: 6px;
  font-family: 'Fira Code', monospace;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.guarantees-explanation {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.guarantee-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.guarantee-item.safe {
  background: #f0fff4;
  border-left: 4px solid #48bb78;
}

.guarantee-item.unsafe {
  background: #fff5f5;
  border-left: 4px solid #e53e3e;
}

/* 字节码并发样式 */
.bytecode-concurrency-showcase {
  padding: 2rem;
}

.lost-update-demo {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.source-code,
.bytecode-breakdown {
  margin: 1rem 0;
}

.problem-scenario {
  background: #fff5f5;
  border: 2px solid #fed7d7;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
}

.thread-execution {
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.execution-steps {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.step {
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
}

.step.interrupted {
  background: #fffaf0;
  border-left: 4px solid #ed8936;
  color: #ed8936;
}

.step.problem {
  background: #fff5f5;
  border-left: 4px solid #e53e3e;
  color: #e53e3e;
  font-weight: bold;
}

.sync-bytecode-analysis {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.sync-source,
.sync-bytecode,
.monitor-instructions {
  margin: 1.5rem 0;
}

.unsafe-read-demo {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.tear-demo {
  background: #fff5f5;
  border: 2px solid #fed7d7;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
}

.tear-steps {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.volatile-implementation {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.volatile-guarantees {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

/* 章节总结样式 */
.chapter-summary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  overflow: hidden;
}

.summary-content {
  padding: 2rem;
  color: white;
}

.key-takeaways {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.takeaway-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.takeaway-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.takeaway-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.takeaway-item p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.mind-map-section {
  margin-top: 3rem;
}

.mindmap-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mindmap-container h3 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.mindmap-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.mermaid-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Mermaid图表样式覆盖 */
.mindmap-wrapper .mermaid {
  max-width: 100%;
  height: auto;
}

.mindmap-wrapper svg {
  max-width: 100%;
  height: auto;
}

.mindmap-placeholder {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.mindmap-placeholder p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
    order: 2;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .performance-evolution {
    flex-direction: column;
  }

  .evolution-arrow {
    transform: rotate(90deg);
  }

  .deadlock-code {
    grid-template-columns: 1fr;
  }

  .state-flow {
    flex-direction: column;
    align-items: stretch;
  }

  .flow-arrow {
    text-align: center;
    transform: rotate(90deg);
  }

  .examples-grid {
    grid-template-columns: 1fr;
  }

  .memory-layout {
    grid-template-columns: 1fr;
  }

  .mindmap-wrapper {
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .pillar-grid,
  .rules-grid {
    grid-template-columns: 1fr;
  }

  .formula-legend {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>
