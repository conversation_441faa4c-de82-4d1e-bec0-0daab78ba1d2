<template>
  <div class="java-chapter6">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第六章：JDK并发库 (JDK Concurrency Libraries)</h1>
          <p class="chapter-subtitle">从原子操作到异步编程：现代并发工具全景</p>
          <div class="chapter-badge">
            <span class="badge-text">JUC Concurrency</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 现代并发应用构建基石 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="现代并发应用构建基石 (Building blocks for modern concurrent applications)"
                :concept-data="modernConcurrencyData"
                @interaction="handleInteraction"
              >
                <div class="modern-concurrency-showcase">
                  <h3>🏗️ 现代并发应用构建基石</h3>

                  <div class="juc-evolution">
                    <h4>📈 JUC包的诞生背景</h4>
                    <div class="evolution-timeline">
                      <div class="timeline-item past">
                        <div class="timeline-header">
                          <span class="timeline-icon">⚰️</span>
                          <h5>Java 1.4及之前</h5>
                        </div>
                        <div class="timeline-content">
                          <p>只有原始的并发工具</p>
                          <div class="evolution-details">
                            <h6>可用工具</h6>
                            <ul>
                              <li>synchronized关键字</li>
                              <li>wait/notify机制</li>
                              <li>Thread类基本操作</li>
                              <li>volatile变量</li>
                            </ul>
                            <h6>主要限制</h6>
                            <ul>
                              <li>功能单一，表达力不足</li>
                              <li>性能开销较大</li>
                              <li>复杂协作难以实现</li>
                              <li>容易出错，调试困难</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="timeline-arrow">→</div>

                      <div class="timeline-item present">
                        <div class="timeline-header">
                          <span class="timeline-icon">🚀</span>
                          <h5>Java 5+ (JUC时代)</h5>
                        </div>
                        <div class="timeline-content">
                          <p>专业并发工具库</p>
                          <div class="evolution-details">
                            <h6>核心组件</h6>
                            <ul>
                              <li>原子类 (Atomic*)</li>
                              <li>显式锁 (Lock/Condition)</li>
                              <li>并发容器 (ConcurrentHashMap等)</li>
                              <li>线程池 (Executor框架)</li>
                              <li>同步器 (CountDownLatch等)</li>
                            </ul>
                            <h6>设计理念</h6>
                            <ul>
                              <li>分离关注点与显式化</li>
                              <li>基于CAS的无锁算法</li>
                              <li>AQS框架的统一抽象</li>
                              <li>函数式异步编程</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="juc-principles">
                    <h4>💡 JUC设计原理</h4>
                    <div class="principles-grid">
                      <div class="principle-card performance">
                        <div class="principle-header">
                          <span class="principle-icon">⚡</span>
                          <h5>高性能</h5>
                        </div>
                        <div class="principle-content">
                          <p>通过无锁算法和精细化锁定策略实现最优性能</p>
                          <div class="principle-details">
                            <h6>核心技术</h6>
                            <ul>
                              <li>CAS (Compare-And-Swap) 原子操作</li>
                              <li>分段锁 (Lock Striping)</li>
                              <li>写时复制 (Copy-On-Write)</li>
                              <li>无锁数据结构</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="principle-card flexibility">
                        <div class="principle-header">
                          <span class="principle-icon">🔧</span>
                          <h5>高灵活性</h5>
                        </div>
                        <div class="principle-content">
                          <p>提供显式API替代语言关键字，支持复杂场景</p>
                          <div class="principle-details">
                            <h6>功能特性</h6>
                            <ul>
                              <li>可中断的锁获取</li>
                              <li>公平/非公平锁选择</li>
                              <li>超时等待机制</li>
                              <li>多条件变量支持</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="principle-card composability">
                        <div class="principle-header">
                          <span class="principle-icon">🧩</span>
                          <h5>可组合性</h5>
                        </div>
                        <div class="principle-content">
                          <p>通过统一的抽象框架支持复杂并发模式</p>
                          <div class="principle-details">
                            <h6>设计模式</h6>
                            <ul>
                              <li>生产者-消费者模式</li>
                              <li>读写分离模式</li>
                              <li>Future/Promise模式</li>
                              <li>线程池化模式</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="juc-architecture">
                    <h4>🏛️ JUC架构全览</h4>
                    <div class="architecture-diagram">
                      <div class="arch-layer application">
                        <h5>应用层</h5>
                        <div class="arch-components">
                          <span class="component">Business Logic</span>
                          <span class="component">Service Layer</span>
                          <span class="component">Data Access</span>
                        </div>
                      </div>

                      <div class="arch-arrow">↓</div>

                      <div class="arch-layer juc-tools">
                        <h5>JUC工具层</h5>
                        <div class="arch-grid">
                          <div class="arch-group">
                            <h6>执行器</h6>
                            <span>Executor</span>
                            <span>ThreadPool</span>
                            <span>ForkJoin</span>
                          </div>
                          <div class="arch-group">
                            <h6>异步编程</h6>
                            <span>Future</span>
                            <span>CompletableFuture</span>
                            <span>CompletionService</span>
                          </div>
                          <div class="arch-group">
                            <h6>并发容器</h6>
                            <span>ConcurrentHashMap</span>
                            <span>CopyOnWriteArrayList</span>
                            <span>BlockingQueue</span>
                          </div>
                          <div class="arch-group">
                            <h6>同步器</h6>
                            <span>CountDownLatch</span>
                            <span>CyclicBarrier</span>
                            <span>Semaphore</span>
                          </div>
                        </div>
                      </div>

                      <div class="arch-arrow">↓</div>

                      <div class="arch-layer foundation">
                        <h5>基础层</h5>
                        <div class="arch-components">
                          <span class="component">AQS框架</span>
                          <span class="component">原子类</span>
                          <span class="component">Lock接口</span>
                          <span class="component">Condition</span>
                        </div>
                      </div>

                      <div class="arch-arrow">↓</div>

                      <div class="arch-layer hardware">
                        <h5>硬件层</h5>
                        <div class="arch-components">
                          <span class="component">CAS指令</span>
                          <span class="component">内存屏障</span>
                          <span class="component">缓存一致性</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 原子类 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="原子类 (Atomic classes)"
                :concept-data="atomicClassesData"
                @interaction="handleInteraction"
              >
                <div class="atomic-classes-showcase">
                  <h3>⚛️ 原子类：无锁并发的基石</h3>

                  <div class="atomic-motivation">
                    <h4>🎯 为什么需要原子类？</h4>
                    <div class="motivation-comparison">
                      <div class="comparison-item problem">
                        <div class="comparison-header">
                          <span class="comparison-icon">❌</span>
                          <h5>传统方案的困境</h5>
                        </div>
                        <div class="comparison-content">
                          <h6>volatile的局限性</h6>
                          <ul>
                            <li>只保证可见性，不保证原子性</li>
                            <li>i++ 这样的复合操作仍不安全</li>
                            <li>无法解决丢失更新问题</li>
                          </ul>
                          <h6>synchronized的开销</h6>
                          <ul>
                            <li>涉及操作系统互斥量，开销大</li>
                            <li>线程挂起和唤醒的额外成本</li>
                            <li>对于简单操作来说过于重量级</li>
                          </ul>
                        </div>
                      </div>

                      <div class="comparison-arrow">→</div>

                      <div class="comparison-item solution">
                        <div class="comparison-header">
                          <span class="comparison-icon">✅</span>
                          <h5>原子类的优势</h5>
                        </div>
                        <div class="comparison-content">
                          <h6>无锁并发</h6>
                          <ul>
                            <li>基于硬件CAS指令，性能优越</li>
                            <li>避免线程挂起，减少上下文切换</li>
                            <li>乐观锁思想，适合低竞争场景</li>
                          </ul>
                          <h6>原子性保证</h6>
                          <ul>
                            <li>复合操作的原子性执行</li>
                            <li>ABA问题的解决方案</li>
                            <li>丰富的原子操作API</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for Atomic Classes -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题：高并发计数器性能瓶颈</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              在实现高并发的全局性能指标计数器（如QPS统计、交易量统计）时，开发者初期使用
                              <code>AtomicLong</code
                              >。但在流量洪峰期，发现计数器所在的服务节点CPU使用率飙升至100%，业务吞吐量却上不去。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              这是典型的
                              <strong>CAS在极高竞争下的性能衰减</strong>
                              问题。成百上千个线程同时更新同一个
                              <code>AtomicLong</code>，导致绝大多数线程的
                              <code>compareAndSet</code> 操作失败，然后立即进入下一次
                              <code>for(;;)</code>
                              循环重试。这种密集的、无效的自旋操作疯狂消耗CPU周期，却很少有线程能成功推进，形成了事实上的"活锁"。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">推荐</span>
                                <h7>方案A: LongAdder (Java 8+ 首选)</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong>：空间换时间，分散热点。内部维护
                                  <code>Cell</code> 数组和
                                  <code>base</code> 字段，在低竞争时直接CAS更新
                                  <code>base</code>，竞争加剧时将线程哈希到不同
                                  <code>Cell</code> 进行更新。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>高并发下吞吐量远超 AtomicLong</li>
                                      <li>CPU消耗显著降低</li>
                                      <li>官方优化，性能极致</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>
                                        <code>sum()</code> 成本比 <code>AtomicLong.get()</code> 略高
                                      </li>
                                      <li>返回的是"可能不完全精确的瞬时快照"</li>
                                      <li>内存占用稍大</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong>：对于高并发计数场景，<code
                                    >LongAdder</code
                                  >
                                  是当前业界无可争议的最佳实践。微小的精度损失在统计场景下完全可接受。
                                </div>
                              </div>
                            </div>

                            <div class="solution-option alternative">
                              <div class="solution-header">
                                <span class="solution-badge">备选</span>
                                <h7>方案B: 分段AtomicLong数组 (手动实现)</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong>：手动创建
                                  <code>AtomicLong</code> 数组，通过
                                  <code>Thread.currentThread().getId() % array.length</code>
                                  散列更新压力。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>能有效缓解单点竞争</li>
                                      <li>原理与LongAdder类似</li>
                                      <li>可自定义散列策略</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>实现复杂度高</li>
                                      <li>可能存在伪共享问题</li>
                                      <li>性能优化不如官方极致</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong>：在
                                  <code>LongAdder</code>
                                  出现之前的历史解决方案，现在已无必要手动实现。
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="cas-principle">
                    <h4>🔄 CAS原理深度解析</h4>
                    <div class="cas-explanation">
                      <div class="cas-formula">
                        <h5>CAS操作三要素</h5>
                        <div class="formula-display">
                          <div class="cas-params">
                            <div class="param-item">
                              <span class="param-symbol">V</span>
                              <span class="param-desc">内存位置 (Variable)</span>
                            </div>
                            <div class="param-item">
                              <span class="param-symbol">A</span>
                              <span class="param-desc">预期值 (Expected)</span>
                            </div>
                            <div class="param-item">
                              <span class="param-symbol">B</span>
                              <span class="param-desc">新值 (New Value)</span>
                            </div>
                          </div>
                          <div class="cas-logic">
                            <code>if (V == A) { V = B; return true; } else { return false; }</code>
                          </div>
                        </div>
                      </div>

                      <div class="cas-demo">
                        <h5>🎭 CAS执行示例</h5>
                        <div class="demo-scenario">
                          <div class="scenario-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                              <h6>初始状态</h6>
                              <p>内存中的值 V = 100</p>
                              <div class="memory-visual">
                                <span class="memory-address">内存地址 0x1234</span>
                                <span class="memory-value">值: 100</span>
                              </div>
                            </div>
                          </div>

                          <div class="scenario-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                              <h6>线程A操作</h6>
                              <p>读取值100，计算101，准备CAS(100, 101)</p>
                              <div class="thread-action thread-a">
                                <span>Thread A: 期望100 → 设置101</span>
                              </div>
                            </div>
                          </div>

                          <div class="scenario-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                              <h6>线程B抢先</h6>
                              <p>线程B成功执行CAS(100, 200)</p>
                              <div class="thread-action thread-b">
                                <span>Thread B: CAS(100, 200) ✅ 成功</span>
                              </div>
                              <div class="memory-visual updated">
                                <span class="memory-address">内存地址 0x1234</span>
                                <span class="memory-value">值: 200</span>
                              </div>
                            </div>
                          </div>

                          <div class="scenario-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                              <h6>线程A重试</h6>
                              <p>CAS(100, 101)失败，重新读取200，计算201，执行CAS(200, 201)成功</p>
                              <div class="thread-action thread-a retry">
                                <span>Thread A: CAS(100, 101) ❌ 失败</span>
                                <span>Thread A: CAS(200, 201) ✅ 成功</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="atomic-types">
                    <h4>🧱 原子类型大全</h4>
                    <div class="types-grid">
                      <div class="type-category basic">
                        <div class="category-header">
                          <span class="category-icon">🔢</span>
                          <h5>基本原子类</h5>
                        </div>
                        <div class="type-list">
                          <div class="type-item">
                            <h6>AtomicInteger</h6>
                            <p>32位整数的原子操作</p>
                            <div class="api-examples">
                              <code>get(), set(), getAndIncrement()</code>
                              <code>compareAndSet(), addAndGet()</code>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>AtomicLong</h6>
                            <p>64位长整数的原子操作</p>
                            <div class="api-examples">
                              <code>incrementAndGet(), decrementAndGet()</code>
                              <code>getAndAdd(), updateAndGet()</code>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>AtomicBoolean</h6>
                            <p>布尔值的原子操作</p>
                            <div class="api-examples">
                              <code>compareAndSet(false, true)</code>
                              <code>getAndSet(), lazySet()</code>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="type-category reference">
                        <div class="category-header">
                          <span class="category-icon">📎</span>
                          <h5>引用原子类</h5>
                        </div>
                        <div class="type-list">
                          <div class="type-item">
                            <h6>AtomicReference&lt;T&gt;</h6>
                            <p>对象引用的原子操作</p>
                            <div class="api-examples">
                              <code>compareAndSet(expect, update)</code>
                              <code>getAndUpdate(Function)</code>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>AtomicStampedReference</h6>
                            <p>带版本戳的引用，解决ABA问题</p>
                            <div class="api-examples">
                              <code>compareAndSet(ref, newRef, stamp, newStamp)</code>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>AtomicMarkableReference</h6>
                            <p>带标记位的引用</p>
                            <div class="api-examples">
                              <code>compareAndSet(ref, newRef, mark, newMark)</code>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="type-category adder">
                        <div class="category-header">
                          <span class="category-icon">➕</span>
                          <h5>高性能累加器</h5>
                        </div>
                        <div class="type-list">
                          <div class="type-item highlight">
                            <h6>LongAdder</h6>
                            <p>高并发场景下的终极计数器</p>
                            <div class="api-examples">
                              <code>increment(), add(x), sum()</code>
                              <code>reset(), sumThenReset()</code>
                            </div>
                            <div class="performance-note">
                              <span class="perf-icon">🚀</span>
                              <span>高竞争时性能远超AtomicLong</span>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>DoubleAdder</h6>
                            <p>双精度浮点数累加器</p>
                            <div class="api-examples">
                              <code>add(x), sum(), reset()</code>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 显式锁与条件 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="显式锁与条件 (Lock classes & Condition objects)"
                :concept-data="lockConditionData"
                @interaction="handleInteraction"
              >
                <div class="lock-condition-showcase">
                  <h3>🔐 显式锁与条件：精确的线程协作</h3>

                  <div class="lock-evolution">
                    <h4>🚀 从synchronized到ReentrantLock的进化</h4>
                    <div class="evolution-comparison">
                      <div class="comparison-side traditional">
                        <div class="side-header">
                          <span class="side-icon">🔒</span>
                          <h5>synchronized (隐式锁)</h5>
                        </div>
                        <div class="side-content">
                          <div class="features">
                            <h6>特性</h6>
                            <ul>
                              <li>JVM内置，使用简单</li>
                              <li>自动获取和释放</li>
                              <li>支持重入</li>
                              <li>现代JVM优化良好</li>
                            </ul>
                          </div>
                          <div class="limitations">
                            <h6>局限性</h6>
                            <ul>
                              <li>无法中断等待锁的线程</li>
                              <li>无法尝试非阻塞获取锁</li>
                              <li>无法设置获取锁的超时</li>
                              <li>只支持非公平锁</li>
                              <li>wait/notify机制复杂易错</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="evolution-arrow">→</div>

                      <div class="comparison-side modern">
                        <div class="side-header">
                          <span class="side-icon">🗝️</span>
                          <h5>ReentrantLock (显式锁)</h5>
                        </div>
                        <div class="side-content">
                          <div class="features">
                            <h6>高级特性</h6>
                            <ul>
                              <li>可中断的锁获取 (lockInterruptibly)</li>
                              <li>尝试获取锁 (tryLock)</li>
                              <li>超时获取锁 (tryLock with timeout)</li>
                              <li>公平锁和非公平锁选择</li>
                              <li>多个Condition条件变量</li>
                            </ul>
                          </div>
                          <div class="responsibilities">
                            <h6>使用责任</h6>
                            <ul>
                              <li>必须手动释放锁</li>
                              <li>需要try-finally保证释放</li>
                              <li>更复杂的API学习成本</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for Locks -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题：盲目替换synchronized导致性能下降</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              很多刚接触JUC的团队，会将代码中所有的
                              <code>synchronized</code> 简单地替换为
                              <code>ReentrantLock</code
                              >，期望获得性能提升，但结果往往事与愿违，有时甚至性能更差，并且代码变得更复杂。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              这种"一刀切"的做法忽略了
                              <code>synchronized</code>
                              在无竞争或低竞争场景下的性能优势。JVM的锁升级机制（偏向锁 → 轻量级锁 →
                              重量级锁）使得 <code>synchronized</code> 在大部分时间里开销极低。而
                              <code>ReentrantLock</code>
                              基于AQS，虽然功能强大，但其基础开销（如CAS操作、维护等待队列）相对较高。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">推荐</span>
                                <h7>方案A: 功能驱动替换</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心原则</strong>：只在需要
                                  <code>ReentrantLock</code> 独有功能时才进行替换。
                                </p>
                                <div class="replacement-scenarios">
                                  <h8>替换场景</h8>
                                  <ul>
                                    <li>需要公平锁，保证等待最久的线程最先获取锁</li>
                                    <li>需要可中断的锁获取，避免线程无限期死等</li>
                                    <li>需要尝试获取锁，在获取不到锁时执行其他逻辑</li>
                                    <li>需要与Condition配合，实现复杂的等待/通知模式</li>
                                  </ul>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong>：这是一种基于需求的务实策略，承认
                                  <code>synchronized</code> 的价值，把JUC
                                  Lock当作解决特定问题的"瑞士军刀"。
                                </div>
                              </div>
                            </div>

                            <div class="solution-option alternative">
                              <div class="solution-header">
                                <span class="solution-badge">备选</span>
                                <h7>方案B: 性能分析驱动替换</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong
                                  >：使用JMH等专业性能测试工具，针对具体业务热点代码，分别测试两种锁在不同并发度下的性能表现。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>最科学、最可靠的方式</li>
                                      <li>基于真实数据做决策</li>
                                      <li>避免主观猜测</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>需要投入时间学习基准测试</li>
                                      <li>有一定技术门槛</li>
                                      <li>测试环境可能与生产环境有差异</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：对于性能敏感的核心模块，这种投入是绝对值得的。但对于一般业务代码，方案A更实用。
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="condition-mechanism">
                    <h4>🎯 Condition：精确的线程协作</h4>
                    <div class="condition-explanation">
                      <div class="condition-advantages">
                        <h5>相比wait/notify的优势</h5>
                        <div class="advantages-grid">
                          <div class="advantage-item">
                            <div class="advantage-icon">🎯</div>
                            <div class="advantage-content">
                              <h6>精确唤醒</h6>
                              <p>
                                可以创建多个Condition，针对不同条件精确唤醒特定线程，避免虚假唤醒
                              </p>
                            </div>
                          </div>
                          <div class="advantage-item">
                            <div class="advantage-icon">🔧</div>
                            <div class="advantage-content">
                              <h6>更强表达力</h6>
                              <p>await()、signal()、signalAll()语义更清晰，代码可读性更好</p>
                            </div>
                          </div>
                          <div class="advantage-item">
                            <div class="advantage-icon">⏰</div>
                            <div class="advantage-content">
                              <h6>超时支持</h6>
                              <p>支持带超时的等待，避免线程无限期阻塞</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 同步辅助工具 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="同步辅助工具 (CountDownLatch & Synchronizers)"
                :concept-data="synchronizersData"
                @interaction="handleInteraction"
              >
                <div class="synchronizers-showcase">
                  <h3>🎯 同步辅助工具：线程协作的艺术</h3>

                  <div class="synchronizers-overview">
                    <h4>🧩 同步器家族全览</h4>
                    <div class="synchronizers-grid">
                      <div class="synchronizer-card countdown">
                        <div class="card-header">
                          <span class="card-icon">⏳</span>
                          <h5>CountDownLatch</h5>
                          <span class="card-badge">一次性</span>
                        </div>
                        <div class="card-content">
                          <p><strong>核心思想</strong>：倒计时门闩，等待N个事件完成</p>
                          <div class="use-cases">
                            <h6>典型场景</h6>
                            <ul>
                              <li>主线程等待多个工作线程完成初始化</li>
                              <li>并行任务的汇总点</li>
                              <li>服务启动协调</li>
                            </ul>
                          </div>
                          <div class="key-methods">
                            <h6>关键方法</h6>
                            <code>countDown()</code> <code>await()</code>
                          </div>
                        </div>
                      </div>

                      <div class="synchronizer-card barrier">
                        <div class="card-header">
                          <span class="card-icon">🚧</span>
                          <h5>CyclicBarrier</h5>
                          <span class="card-badge">可重用</span>
                        </div>
                        <div class="card-content">
                          <p><strong>核心思想</strong>：循环屏障，等待所有线程到达同步点</p>
                          <div class="use-cases">
                            <h6>典型场景</h6>
                            <ul>
                              <li>多线程分阶段计算</li>
                              <li>并行算法的同步点</li>
                              <li>游戏中的回合制同步</li>
                            </ul>
                          </div>
                          <div class="key-methods">
                            <h6>关键方法</h6>
                            <code>await()</code> <code>reset()</code>
                          </div>
                        </div>
                      </div>

                      <div class="synchronizer-card semaphore">
                        <div class="card-header">
                          <span class="card-icon">🎫</span>
                          <h5>Semaphore</h5>
                          <span class="card-badge">计数型</span>
                        </div>
                        <div class="card-content">
                          <p><strong>核心思想</strong>：信号量，控制同时访问资源的线程数量</p>
                          <div class="use-cases">
                            <h6>典型场景</h6>
                            <ul>
                              <li>连接池大小限制</li>
                              <li>限流器实现</li>
                              <li>资源访问控制</li>
                            </ul>
                          </div>
                          <div class="key-methods">
                            <h6>关键方法</h6>
                            <code>acquire()</code> <code>release()</code>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for Synchronizers -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题：CountDownLatch使用不当导致死锁</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              在微服务启动场景中，主线程使用
                              <code>CountDownLatch</code>
                              等待多个组件初始化完成。但某个组件初始化失败抛异常，没有调用
                              <code>countDown()</code>，导致主线程永远阻塞在
                              <code>await()</code>，整个服务无法启动。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              <code>CountDownLatch</code>
                              是一次性的，计数器不会自动重置。如果某个线程异常退出而没有调用
                              <code>countDown()</code
                              >，等待的线程将永远阻塞。这在异常处理不完善的代码中很容易发生。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">推荐</span>
                                <h7>方案A: try-finally保证countDown</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong
                                  >：无论初始化成功还是失败，都要在finally块中调用
                                  <code>countDown()</code>。
                                </p>
                                <div class="code-example">
                                  <pre><code>// 工作线程中
try {
    // 执行初始化逻辑
    initializeComponent();
} finally {
    // 无论成功失败都要countDown
    latch.countDown();
}</code></pre>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：简单可靠，是最基础的防护措施。但需要在业务逻辑中区分初始化是否真正成功。
                                </div>
                              </div>
                            </div>

                            <div class="solution-option alternative">
                              <div class="solution-header">
                                <span class="solution-badge">备选</span>
                                <h7>方案B: 带超时的await</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong>：使用
                                  <code>await(timeout, TimeUnit)</code> 避免无限期等待。
                                </p>
                                <div class="code-example">
                                  <pre><code>// 主线程中
if (!latch.await(30, TimeUnit.SECONDS)) {
    // 超时处理逻辑
    throw new TimeoutException("组件初始化超时");
}</code></pre>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：提供了兜底保护，但需要合理设置超时时间，过短可能误判，过长可能影响故障恢复速度。
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="countdown-latch-deep-dive">
                    <h4>⏳ CountDownLatch深度解析</h4>
                    <div class="countdown-explanation">
                      <div class="countdown-workflow">
                        <h5>🔄 工作流程</h5>
                        <div class="workflow-steps">
                          <div class="workflow-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                              <h6>初始化</h6>
                              <p>创建CountDownLatch(n)，内部计数器设为n</p>
                            </div>
                          </div>
                          <div class="workflow-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                              <h6>工作线程</h6>
                              <p>完成任务后调用countDown()，计数器减1</p>
                            </div>
                          </div>
                          <div class="workflow-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                              <h6>等待线程</h6>
                              <p>调用await()阻塞，直到计数器归零</p>
                            </div>
                          </div>
                          <div class="workflow-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                              <h6>全部唤醒</h6>
                              <p>计数器归零时，所有等待线程被唤醒</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: 并发容器 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="并发容器 (Concurrent containers)"
                :concept-data="concurrentContainersData"
                @interaction="handleInteraction"
              >
                <div class="concurrent-containers-showcase">
                  <h3>📦 并发容器：高性能数据结构的艺术</h3>

                  <div class="containers-overview">
                    <h4>🏗️ 并发容器家族</h4>
                    <div class="containers-grid">
                      <div class="container-card concurrent-map">
                        <div class="card-header">
                          <span class="card-icon">🗺️</span>
                          <h5>ConcurrentHashMap</h5>
                          <span class="card-badge">高并发读写</span>
                        </div>
                        <div class="card-content">
                          <p><strong>设计理念</strong>：分段锁策略，最小化锁竞争</p>
                          <div class="evolution-timeline">
                            <div class="timeline-item">
                              <h6>Java 7: 分段锁</h6>
                              <p>Segment数组 + HashEntry链表</p>
                            </div>
                            <div class="timeline-item">
                              <h6>Java 8+: CAS + Node锁</h6>
                              <p>数组 + 链表/红黑树 + synchronized</p>
                            </div>
                          </div>
                          <div class="performance-highlights">
                            <h6>性能特点</h6>
                            <ul>
                              <li>读操作几乎无锁</li>
                              <li>写操作精细化锁定</li>
                              <li>扩容时渐进式迁移</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="container-card copy-on-write">
                        <div class="card-header">
                          <span class="card-icon">📋</span>
                          <h5>CopyOnWriteArrayList</h5>
                          <span class="card-badge">读多写少</span>
                        </div>
                        <div class="card-content">
                          <p><strong>设计理念</strong>：写时复制，读操作完全无锁</p>
                          <div class="cow-mechanism">
                            <h6>工作机制</h6>
                            <div class="mechanism-steps">
                              <div class="step">
                                <span class="step-num">1</span>
                                <span>读操作直接访问数组，无需加锁</span>
                              </div>
                              <div class="step">
                                <span class="step-num">2</span>
                                <span>写操作复制整个数组</span>
                              </div>
                              <div class="step">
                                <span class="step-num">3</span>
                                <span>在副本上修改，然后原子替换引用</span>
                              </div>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <h6>适用场景</h6>
                            <ul>
                              <li>读操作远多于写操作</li>
                              <li>迭代器需要安全遍历</li>
                              <li>可以容忍写操作的高成本</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for Concurrent Containers -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题：CopyOnWriteArrayList内存溢出</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              在一个事件监听器管理系统中，使用
                              <code>CopyOnWriteArrayList</code>
                              存储监听器列表。在高频添加/删除监听器的场景下，系统出现内存溢出，GC频繁，应用性能急剧下降。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              <code>CopyOnWriteArrayList</code>
                              每次写操作都会复制整个数组，在频繁写入的场景下会产生大量临时对象。如果数组较大且写操作频繁，会导致内存使用量激增，GC压力巨大。这违背了"读多写少"的使用前提。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">推荐</span>
                                <h7>方案A: 场景驱动的容器选择</h7>
                              </div>
                              <div class="solution-details">
                                <p><strong>核心原则</strong>：根据读写比例选择合适的并发容器。</p>
                                <div class="container-selection">
                                  <h8>选择指南</h8>
                                  <ul>
                                    <li><strong>读多写少</strong>：CopyOnWriteArrayList</li>
                                    <li>
                                      <strong>读写均衡</strong>：Collections.synchronizedList +
                                      外部同步
                                    </li>
                                    <li>
                                      <strong>高并发读写</strong>：ConcurrentLinkedQueue
                                      (如果只需队列语义)
                                    </li>
                                    <li>
                                      <strong>复杂场景</strong>：ReentrantReadWriteLock + ArrayList
                                    </li>
                                  </ul>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：没有银弹，必须基于具体的读写模式和性能要求选择。
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 6: 阻塞队列 -->
            <section id="topic-5" class="topic-section" ref="topic5">
              <ExpandableSection
                title="阻塞队列 (Blocking queues)"
                :concept-data="blockingQueuesData"
                @interaction="handleInteraction"
              >
                <div class="blocking-queues-showcase">
                  <h3>🚦 阻塞队列：生产者-消费者的完美桥梁</h3>

                  <div class="queues-comparison">
                    <h4>⚖️ 主流阻塞队列对比</h4>
                    <div class="comparison-table">
                      <div class="table-header">
                        <div class="header-cell">队列类型</div>
                        <div class="header-cell">底层结构</div>
                        <div class="header-cell">容量</div>
                        <div class="header-cell">锁策略</div>
                        <div class="header-cell">适用场景</div>
                      </div>

                      <div class="table-row">
                        <div class="cell queue-name">ArrayBlockingQueue</div>
                        <div class="cell">数组</div>
                        <div class="cell">有界</div>
                        <div class="cell">单锁 + 双Condition</div>
                        <div class="cell">固定容量，性能稳定</div>
                      </div>

                      <div class="table-row">
                        <div class="cell queue-name">LinkedBlockingQueue</div>
                        <div class="cell">链表</div>
                        <div class="cell">可有界/无界</div>
                        <div class="cell">双锁分离</div>
                        <div class="cell">高并发，动态容量</div>
                      </div>

                      <div class="table-row">
                        <div class="cell queue-name">PriorityBlockingQueue</div>
                        <div class="cell">堆</div>
                        <div class="cell">无界</div>
                        <div class="cell">单锁</div>
                        <div class="cell">优先级排序</div>
                      </div>

                      <div class="table-row">
                        <div class="cell queue-name">SynchronousQueue</div>
                        <div class="cell">无存储</div>
                        <div class="cell">0</div>
                        <div class="cell">CAS</div>
                        <div class="cell">直接传递</div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- 章节总结 -->
            <section class="chapter-summary">
              <div class="summary-content">
                <h2>🎯 第六章核心要点总结</h2>
                <div class="key-takeaways">
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🏗️</div>
                    <div>
                      <h4>JUC包的革命性意义</h4>
                      <p>
                        从synchronized的"手动挡"升级到JUC的"自动挡"，提供了更高性能、更灵活的并发编程工具集
                      </p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">⚛️</div>
                    <div>
                      <h4>原子类的无锁优势</h4>
                      <p>
                        基于CAS的无锁算法在低到中等竞争下性能卓越，LongAdder是高并发计数的最佳实践
                      </p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🔐</div>
                    <div>
                      <h4>显式锁的强大功能</h4>
                      <p>
                        ReentrantLock提供了公平性、可中断、超时等高级特性，Condition实现了精确的线程协作
                      </p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">📦</div>
                    <div>
                      <h4>并发容器的分化设计</h4>
                      <p>ConcurrentHashMap针对高并发读写，CopyOnWriteArrayList优化读多写少场景</p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🎯</div>
                    <div>
                      <h4>同步辅助工具的协作艺术</h4>
                      <p>CountDownLatch、CyclicBarrier、Semaphore各有专长，简化复杂线程协作场景</p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🏭</div>
                    <div>
                      <h4>项目实践踩坑与解决方案</h4>
                      <p>从高并发计数器性能瓶颈到锁选择策略，业界最佳实践助力避坑</p>
                    </div>
                  </div>
                </div>

                <!-- 思维导图部分 -->
                <div class="mind-map-section">
                  <div class="mindmap-container">
                    <h3>🧠 第六章知识脉络图</h3>
                    <div class="mindmap-wrapper">
                      <div id="chapter6-mindmap" class="mermaid-container">
                        <div class="mindmap-placeholder">
                          <p>🎨 正在生成思维导图...</p>
                          <p>如果长时间未显示，请刷新页面</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: '现代并发构建基石',
    description: 'JUC包的设计理念与架构全览',
  },
  {
    title: '原子类详解',
    description: 'CAS机制与无锁编程实践',
  },
  {
    title: '显式锁与条件',
    description: 'Lock接口与Condition的强大功能',
  },
  {
    title: '同步辅助工具',
    description: 'CountDownLatch等协作工具',
  },
  {
    title: '并发容器',
    description: 'ConcurrentHashMap与CopyOnWriteArrayList',
  },
  {
    title: '阻塞队列',
    description: '生产者-消费者模式的经典实现',
  },
  {
    title: 'Future与异步编程',
    description: 'CompletableFuture的声明式编程',
  },
  {
    title: 'Executor框架',
    description: '任务执行与线程池管理',
  },
]

// 概念数据
const modernConcurrencyData = {
  title: '现代并发应用构建基石',
  keyPoints: [
    'JUC包是Java并发编程的核心工具集，提供高级并发组件',
    '设计理念：分离关注点与显式化，替代语言关键字',
    '基于CAS无锁算法和AQS框架的统一抽象',
    '从简单原子操作到复杂异步编程的完整工具链',
    '解决synchronized和wait/notify的性能和功能局限',
  ],
}

const atomicClassesData = {
  title: '原子类',
  keyPoints: [
    '原子类基于CAS实现无锁并发，避免synchronized的性能开销',
    'CAS三要素：内存位置V，预期值A，新值B',
    'AtomicInteger/Long提供基本原子操作，API丰富',
    'LongAdder通过分段Cell解决高竞争下的性能问题',
    'AtomicReference支持对象引用的原子操作',
  ],
}

const lockConditionData = {
  title: '显式锁与条件',
  keyPoints: [
    'ReentrantLock提供比synchronized更丰富的功能',
    '支持公平锁、可中断锁获取、尝试获取锁等高级特性',
    'Condition提供精确的线程等待和唤醒机制',
    '基于AQS框架实现，使用CLH队列管理等待线程',
    '必须在finally块中释放锁，避免死锁风险',
  ],
}

const synchronizersData = {
  title: '同步辅助工具',
  keyPoints: [
    'CountDownLatch实现一次性的倒计时同步',
    'CyclicBarrier支持循环使用的屏障同步',
    'Semaphore实现资源访问的数量控制',
    '这些工具简化了复杂的线程协作场景',
    '都基于AQS框架，提供一致的使用体验',
  ],
}

const concurrentContainersData = {
  title: '并发容器',
  keyPoints: [
    'ConcurrentHashMap通过分段锁实现高并发访问',
    'Java 8+版本改用CAS+Node锁，性能进一步提升',
    'CopyOnWriteArrayList适用于读多写少场景',
    '写时复制确保读操作完全无锁，迭代安全',
    '选择合适的并发容器是性能优化的关键',
  ],
}

const blockingQueuesData = {
  title: '阻塞队列',
  keyPoints: [
    '阻塞队列是生产者-消费者模式的核心实现',
    'ArrayBlockingQueue有界基于数组，性能稳定',
    'LinkedBlockingQueue可有界可无界，支持更高并发',
    '提供自然的流量控制和反压机制',
    '避免使用无界队列防止内存溢出',
  ],
}

const futureAsyncData = {
  title: 'Future与异步编程',
  keyPoints: [
    'Future提供异步计算结果的访问接口',
    'CompletableFuture支持链式调用和函数式编程',
    '可组合多个异步操作，实现复杂的异步编排',
    '提供丰富的异常处理和超时控制机制',
    '是响应式编程的基础工具',
  ],
}

const executorData = {
  title: 'Executor框架',
  keyPoints: [
    'Executor框架将任务提交与执行解耦',
    'ThreadPoolExecutor提供灵活的线程池配置',
    '合理设置核心线程数、最大线程数和队列容量',
    '选择合适的拒绝策略处理任务溢出',
    '避免使用Executors工厂方法，手动配置更安全',
  ],
}

// 方法
const scrollToTopic = (index: number) => {
  currentTopic.value = index

  // 滚动到对应的主题部分
  const targetElement = document.getElementById(`topic-${index}`)
  if (targetElement) {
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
}

// 生命周期
onMounted(async () => {
  // 渲染思维导图
  await renderMindMap()

  // 模拟进度更新
  const interval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += 2
    } else {
      clearInterval(interval)
    }
  }, 100)
})

const renderMindMap = async () => {
  try {
    console.log('开始初始化 Mermaid...')
    const mermaid = await import('mermaid')
    console.log('Mermaid 模块加载成功:', mermaid)

    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    })
    console.log('Mermaid 初始化完成')

    // 延迟渲染以确保DOM已加载
    setTimeout(async () => {
      try {
        console.log('开始渲染 Mermaid 图表...')

        // 创建思维导图内容
        const mindmapContent = `mindmap
  root((第六章 JDK并发库))
    现代并发基石
      JUC包意义
        解放synchronized局限
        显式化API设计
        性能与功能并重
      设计原理
        分离关注点
        CAS无锁算法
        AQS统一框架
        函数式编程
      架构层次
        应用层
        JUC工具层
        基础框架层
        硬件指令层
    原子操作
      AtomicInteger/Long
        基本原子操作
        CAS自旋重试
        API丰富易用
      AtomicReference
        对象引用原子性
        ABA问题解决
        函数式更新
      LongAdder
        分段Cell设计
        高竞争性能优化
        空间换时间策略
      核心原理
        CAS三要素
        硬件支持
        乐观锁思想
      项目实践踩坑
        高并发计数器瓶颈
        LongAdder最佳实践
        性能测试驱动选择
    显式锁与协作
      ReentrantLock
        公平非公平选择
        可中断锁获取
        尝试获取超时
      Condition
        精确等待唤醒
        多条件变量
        替代wait notify
      实践要点
        finally释放锁
        避免死锁
        性能vs功能权衡
      项目实践踩坑
        盲目替换synchronized
        功能驱动vs性能驱动
        JMH基准测试验证
    同步辅助工具
      CountDownLatch
        一次性倒计时
        等待N个事件
        启动协调场景
      CyclicBarrier
        循环屏障同步
        阶段性任务
        可重置计数器
      Semaphore
        资源数量控制
        限流器实现
        许可证模式
      项目实践踩坑
        CountDownLatch死锁
        try-finally保证countDown
        带超时的await兜底
    并发容器
      ConcurrentHashMap
        分段锁Java7
        CAS Node锁Java8
        高并发读写优化
      CopyOnWriteArrayList
        写时复制策略
        读多写少优化
        迭代器安全
      选择原则
        场景驱动
        性能测试验证
        读写比例考量
    阻塞队列
      ArrayBlockingQueue
        有界数组实现
        单锁双Condition
        性能稳定
      LinkedBlockingQueue
        可有界链表
        双锁分离
        高并发支持
      应用模式
        生产者消费者
        流量控制
        反压机制
    异步编程
      Future局限
        阻塞get方法
        组合困难
        异常处理不便
      CompletableFuture
        链式调用
        函数式编程
        丰富组合操作
      最佳实践
        避免阻塞等待
        异常处理策略
        超时控制机制
    任务执行框架
      Executor接口
        任务提交执行解耦
        统一抽象
        可扩展设计
      ThreadPoolExecutor
        核心最大线程数
        队列选择策略
        拒绝处理机制
      实践警告
        避免Executors工厂
        手动配置参数
        监控线程池状态`

        const container = document.getElementById('chapter6-mindmap')
        if (container) {
          container.innerHTML = mindmapContent
          await mermaid.default.run({
            nodes: [container],
          })
          console.log('Mermaid 图表渲染成功')
        }
      } catch (error) {
        console.error('渲染 Mermaid 图表时出错:', error)
        const container = document.getElementById('chapter6-mindmap')
        if (container) {
          container.innerHTML = `
            <div class="mindmap-placeholder">
              <p>💡 思维导图渲染遇到问题</p>
              <p>建议刷新页面重新加载</p>
            </div>
          `
        }
      }
    }, 1000)
  } catch (error) {
    console.error('初始化 Mermaid 时出错:', error)
  }
}
</script>

<style scoped>
/* 基础样式 */
.java-chapter6 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部样式 */
.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20s-20-8.954-20-20 8.954-20 20-20 20 8.954 20 20z'/%3E%3C/g%3E%3C/svg%3E");
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.chapter-title {
  font-size: 3rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.chapter-subtitle {
  font-size: 1.3rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  font-weight: 300;
}

.chapter-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.badge-text {
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.progress-bar {
  max-width: 400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 4px;
  position: relative;
}

.progress-fill {
  height: 8px;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 6px;
  transition: width 0.5s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 内容布局 */
.content-wrapper {
  padding: 2rem 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
}

/* 侧边栏样式 */
.sidebar {
  position: sticky;
  top: 2rem;
}

.outline {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  text-align: center;
}

.outline-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.outline-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.outline-item:hover {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.outline-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  color: #666;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.outline-content p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.3;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  background: white;
  color: #333;
  border: 2px solid #e9ecef;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  text-align: center;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #667eea;
  color: #667eea;
}

/* 主内容区样式 */
.main-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.topic-section {
  margin-bottom: 2rem;
}

/* JUC展示样式 */
.modern-concurrency-showcase {
  padding: 2rem;
}

.modern-concurrency-showcase h3 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 2rem;
  text-align: center;
}

.juc-evolution {
  margin: 2rem 0;
}

.juc-evolution h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
}

.evolution-timeline {
  display: flex;
  align-items: stretch;
  gap: 2rem;
  margin: 2rem 0;
}

.timeline-item {
  flex: 1;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.timeline-item.past {
  border-top: 4px solid #e74c3c;
}

.timeline-item.present {
  border-top: 4px solid #27ae60;
}

.timeline-header {
  background: #f8f9fa;
  padding: 1.5rem;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
}

.timeline-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.timeline-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.timeline-content {
  padding: 1.5rem;
}

.timeline-content p {
  margin: 0 0 1rem 0;
  color: #555;
  font-weight: 500;
}

.evolution-details h6 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
  font-size: 0.95rem;
}

.evolution-details ul {
  margin: 0;
  padding-left: 1.5rem;
  list-style-type: disc;
}

.evolution-details li {
  margin-bottom: 0.3rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.timeline-arrow {
  display: flex;
  align-items: center;
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

/* 原理展示样式 */
.juc-principles {
  margin: 3rem 0;
}

.principles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.principle-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.principle-card:hover {
  transform: translateY(-4px);
}

.principle-card.performance {
  border-top: 4px solid #f39c12;
}

.principle-card.flexibility {
  border-top: 4px solid #3498db;
}

.principle-card.composability {
  border-top: 4px solid #9b59b6;
}

.principle-header {
  padding: 1.5rem;
  text-align: center;
  background: #f8f9fa;
}

.principle-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.principle-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.principle-content {
  padding: 1.5rem;
}

.principle-content p {
  margin: 0 0 1rem 0;
  color: #555;
  line-height: 1.6;
}

.principle-details h6 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
  font-size: 0.95rem;
}

.principle-details ul {
  margin: 0;
  padding-left: 1.5rem;
}

.principle-details li {
  margin-bottom: 0.3rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* 架构图样式 */
.juc-architecture {
  margin: 3rem 0;
}

.architecture-diagram {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.arch-layer {
  width: 100%;
  max-width: 800px;
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.arch-layer.application {
  border-left: 4px solid #e74c3c;
}

.arch-layer.juc-tools {
  border-left: 4px solid #3498db;
}

.arch-layer.foundation {
  border-left: 4px solid #f39c12;
}

.arch-layer.hardware {
  border-left: 4px solid #27ae60;
}

.arch-layer h5 {
  margin: 0 0 1rem 0;
  text-align: center;
  color: #333;
  font-size: 1.1rem;
}

.arch-components {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.arch-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

.arch-group {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.arch-group h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
  font-weight: 600;
}

.arch-group span {
  display: block;
  margin: 0.2rem 0;
  color: #666;
  font-size: 0.8rem;
}

.component {
  padding: 0.5rem 1rem;
  background: #e9ecef;
  border-radius: 6px;
  font-size: 0.85rem;
  color: #495057;
}

.arch-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

/* 原子类展示样式 */
.atomic-classes-showcase {
  padding: 2rem;
}

.atomic-motivation {
  margin: 2rem 0;
}

.motivation-comparison {
  display: flex;
  align-items: stretch;
  gap: 2rem;
  margin: 2rem 0;
}

.comparison-item {
  flex: 1;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.comparison-item.problem {
  border-top: 4px solid #e74c3c;
}

.comparison-item.solution {
  border-top: 4px solid #27ae60;
}

.comparison-header {
  background: #f8f9fa;
  padding: 1.5rem;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
}

.comparison-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.comparison-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.comparison-content {
  padding: 1.5rem;
}

.comparison-content h6 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
  font-size: 0.95rem;
}

.comparison-content ul {
  margin: 0 0 1rem 0;
  padding-left: 1.5rem;
}

.comparison-content li {
  margin-bottom: 0.3rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.comparison-arrow {
  display: flex;
  align-items: center;
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

/* CAS原理样式 */
.cas-principle {
  margin: 3rem 0;
}

.cas-explanation {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.cas-formula {
  margin-bottom: 2rem;
}

.cas-formula h5 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.formula-display {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cas-params {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.param-item {
  text-align: center;
}

.param-symbol {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.param-desc {
  font-size: 0.9rem;
  color: #666;
}

.cas-logic {
  text-align: center;
  padding: 1rem;
  background: #2d3748;
  border-radius: 6px;
  margin-top: 1rem;
}

.cas-logic code {
  color: #e2e8f0;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
}

/* CAS演示样式 */
.cas-demo {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-scenario {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.scenario-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.step-content p {
  margin: 0 0 0.5rem 0;
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
}

.memory-visual {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.memory-visual.updated {
  background: #e8f5e8;
}

.memory-address {
  font-family: 'Fira Code', monospace;
  font-size: 0.8rem;
  color: #666;
}

.memory-value {
  font-family: 'Fira Code', monospace;
  font-size: 0.8rem;
  font-weight: bold;
  color: #333;
}

.thread-action {
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Fira Code', monospace;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.thread-action.thread-a {
  background: #e3f2fd;
  color: #1976d2;
}

.thread-action.thread-b {
  background: #f3e5f5;
  color: #7b1fa2;
}

.thread-action.retry {
  background: #fff3e0;
  color: #f57c00;
}

.thread-action span {
  display: block;
  margin: 0.2rem 0;
}

/* 原子类型样式 */
.atomic-types {
  margin: 3rem 0;
}

.types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.type-category {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.type-category.basic {
  border-top: 4px solid #3498db;
}

.type-category.reference {
  border-top: 4px solid #9b59b6;
}

.type-category.adder {
  border-top: 4px solid #e67e22;
}

.category-header {
  background: #f8f9fa;
  padding: 1.5rem;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
}

.category-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.category-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.type-list {
  padding: 1.5rem;
}

.type-item {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.type-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.type-item.highlight {
  background: #fff8e1;
  margin: -0.5rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #ffc107;
}

.type-item h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.type-item p {
  margin: 0 0 0.75rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.api-examples {
  background: #2d3748;
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0.5rem 0;
}

.api-examples code {
  display: block;
  color: #e2e8f0;
  font-family: 'Fira Code', monospace;
  font-size: 0.8rem;
  margin: 0.2rem 0;
}

.performance-note {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.5rem;
  background: #e8f5e8;
  border-radius: 4px;
  border-left: 3px solid #4caf50;
}

.perf-icon {
  font-size: 1rem;
}

.performance-note span:last-child {
  font-size: 0.85rem;
  color: #2e7d32;
  font-weight: 500;
}

/* 章节总结样式 */
.chapter-summary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  overflow: hidden;
  margin-top: 2rem;
}

.summary-content {
  padding: 2rem;
  color: white;
}

.summary-content h2 {
  text-align: center;
  margin: 0 0 2rem 0;
  font-size: 2rem;
}

.key-takeaways {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.takeaway-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.takeaway-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.takeaway-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.takeaway-item p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.mind-map-section {
  margin-top: 3rem;
}

.mindmap-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mindmap-container h3 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.mindmap-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.mermaid-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mindmap-placeholder {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.mindmap-placeholder p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
    order: 2;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .evolution-timeline {
    flex-direction: column;
  }

  .timeline-arrow {
    transform: rotate(90deg);
    text-align: center;
  }

  .motivation-comparison {
    flex-direction: column;
  }

  .comparison-arrow {
    transform: rotate(90deg);
    text-align: center;
  }

  .cas-params {
    flex-direction: column;
    gap: 1rem;
  }

  .types-grid {
    grid-template-columns: 1fr;
  }

  .key-takeaways {
    grid-template-columns: 1fr;
  }

  .mindmap-wrapper {
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .principles-grid {
    grid-template-columns: 1fr;
  }

  .demo-scenario {
    gap: 0.5rem;
  }

  .scenario-step {
    flex-direction: column;
    text-align: center;
  }
}

/* Real-World Problems & Solutions 样式 */
.real-world-section {
  margin: 3rem 0;
  padding: 2rem;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-radius: 12px;
  border-left: 4px solid #e53e3e;
}

.real-world-section h4 {
  color: #c53030;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.problems-solutions {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.problem-case {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.case-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #fed7d7;
}

.case-icon {
  font-size: 1.5rem;
}

.case-header h5 {
  color: #c53030;
  margin: 0;
  font-size: 1.1rem;
}

.case-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.problem-description,
.root-cause {
  padding: 1rem;
  border-radius: 6px;
}

.problem-description {
  background: #fef5e7;
  border-left: 3px solid #f6ad55;
}

.root-cause {
  background: #e6fffa;
  border-left: 3px solid #38b2ac;
}

.problem-description h6,
.root-cause h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.solutions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.solution-option {
  border-radius: 8px;
  padding: 1.5rem;
  border: 2px solid transparent;
}

.solution-option.recommended {
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
  border-color: #48bb78;
}

.solution-option.alternative {
  background: linear-gradient(135deg, #f7fafc 0%, #e2e8f0 100%);
  border-color: #a0aec0;
}

.solution-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.solution-badge {
  background: #48bb78;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.solution-option.alternative .solution-badge {
  background: #a0aec0;
}

.solution-header h7 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.solution-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pros-cons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.pros,
.cons {
  padding: 1rem;
  border-radius: 6px;
}

.pros {
  background: #f0fff4;
  border-left: 3px solid #48bb78;
}

.cons {
  background: #fef5e7;
  border-left: 3px solid #ed8936;
}

.pros h8,
.cons h8 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.2rem;
}

.pros li,
.cons li {
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.trade-offs {
  background: #edf2f7;
  padding: 1rem;
  border-radius: 6px;
  border-left: 3px solid #4299e1;
}

.trade-offs strong {
  color: #2b6cb0;
}

.code-example {
  background: #1a202c;
  border-radius: 6px;
  padding: 1rem;
  margin: 0.5rem 0;
}

.code-example pre {
  margin: 0;
  color: #e2e8f0;
  font-family: 'Fira Code', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
}

.replacement-scenarios h8 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.replacement-scenarios ul {
  margin: 0;
  padding-left: 1.2rem;
}

.replacement-scenarios li {
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

/* 同步器卡片样式 */
.synchronizers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.synchronizer-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #667eea;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.synchronizer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.synchronizer-card.countdown {
  border-top-color: #f6ad55;
}

.synchronizer-card.barrier {
  border-top-color: #4299e1;
}

.synchronizer-card.semaphore {
  border-top-color: #48bb78;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.card-icon {
  font-size: 1.5rem;
}

.card-header h5 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.card-badge {
  background: #edf2f7;
  color: #4a5568;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.card-content p {
  color: #4a5568;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.use-cases,
.key-methods {
  margin-bottom: 1rem;
}

.use-cases h6,
.key-methods h6 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.use-cases ul {
  margin: 0;
  padding-left: 1.2rem;
}

.use-cases li {
  font-size: 0.85rem;
  line-height: 1.4;
  color: #4a5568;
  margin-bottom: 0.25rem;
}

.key-methods code {
  background: #edf2f7;
  color: #2d3748;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}

/* 工作流程样式 */
.workflow-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.workflow-step {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.workflow-step .step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin: 0 auto 1rem auto;
}

.workflow-step h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.workflow-step p {
  color: #4a5568;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
}

/* 锁进化对比样式 */
.evolution-comparison {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
  margin: 2rem 0;
}

.comparison-side {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.comparison-side.traditional {
  border-left: 4px solid #e53e3e;
}

.comparison-side.modern {
  border-left: 4px solid #38a169;
}

.side-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f7fafc;
}

.side-icon {
  font-size: 1.5rem;
}

.side-header h5 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.side-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.features,
.limitations,
.responsibilities {
  padding: 1rem;
  border-radius: 6px;
}

.features {
  background: #f0fff4;
  border-left: 3px solid #48bb78;
}

.limitations {
  background: #fef5e7;
  border-left: 3px solid #ed8936;
}

.responsibilities {
  background: #e6fffa;
  border-left: 3px solid #38b2ac;
}

.features h6,
.limitations h6,
.responsibilities h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  font-size: 0.9rem;
}

.features ul,
.limitations ul,
.responsibilities ul {
  margin: 0;
  padding-left: 1.2rem;
}

.features li,
.limitations li,
.responsibilities li {
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

/* Condition优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.advantage-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.advantage-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.advantage-content h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.advantage-content p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .synchronizers-grid {
    grid-template-columns: 1fr;
  }

  .pros-cons {
    grid-template-columns: 1fr;
  }

  .workflow-steps {
    grid-template-columns: 1fr;
  }

  .evolution-comparison {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .evolution-arrow {
    transform: rotate(90deg);
    text-align: center;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }
}

/* 并发容器样式 */
.containers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.container-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #667eea;
}

.container-card.concurrent-map {
  border-top-color: #4299e1;
}

.container-card.copy-on-write {
  border-top-color: #48bb78;
}

.evolution-timeline {
  margin: 1rem 0;
}

.evolution-timeline .timeline-item {
  background: #f7fafc;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-left: 3px solid #4299e1;
}

.evolution-timeline .timeline-item h6 {
  color: #2d3748;
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.evolution-timeline .timeline-item p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
}

.performance-highlights,
.cow-mechanism,
.trade-offs {
  margin: 1rem 0;
}

.performance-highlights h6,
.cow-mechanism h6,
.trade-offs h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.performance-highlights ul,
.trade-offs ul {
  margin: 0;
  padding-left: 1.2rem;
}

.performance-highlights li,
.trade-offs li {
  font-size: 0.85rem;
  line-height: 1.4;
  color: #4a5568;
  margin-bottom: 0.25rem;
}

.mechanism-steps {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: #edf2f7;
  border-radius: 6px;
}

.step-num {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #48bb78;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  flex-shrink: 0;
}

.step span:last-child {
  font-size: 0.85rem;
  color: #2d3748;
}

.container-selection h8 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.container-selection ul {
  margin: 0;
  padding-left: 1.2rem;
}

.container-selection li {
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

/* 阻塞队列对比表格样式 */
.comparison-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

.table-header {
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1.2fr 1.5fr;
  background: #667eea;
  color: white;
}

.table-row {
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1.2fr 1.5fr;
  border-bottom: 1px solid #e2e8f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:nth-child(even) {
  background: #f7fafc;
}

.header-cell,
.cell {
  padding: 1rem;
  text-align: center;
  font-size: 0.85rem;
}

.header-cell {
  font-weight: 600;
  font-size: 0.9rem;
}

.cell {
  color: #4a5568;
  line-height: 1.4;
}

.queue-name {
  font-weight: 600;
  color: #2d3748;
  text-align: left;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .containers-grid {
    grid-template-columns: 1fr;
  }

  .comparison-table {
    font-size: 0.8rem;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    text-align: left;
  }

  .header-cell,
  .cell {
    padding: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .header-cell {
    background: #667eea;
    color: white;
    font-weight: 600;
  }

  .cell::before {
    content: attr(data-label) ': ';
    font-weight: 600;
    color: #2d3748;
  }
}
</style>
